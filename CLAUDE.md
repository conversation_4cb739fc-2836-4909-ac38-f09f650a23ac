# Development Guidelines

This document contains critical information about working with this codebase.
Follow these guidelines precisely.

## Rules

1. Package Management
   - ONLY use uv, NEVER pip
   - Installation: `uv add package` (production), `uv add --dev package` (dev)
   - Upgrading: `uv add --dev package --upgrade-package package`
   - Running commands: `uv run python -m <command>` when direct command fails
   - IMPORTANT: Some tools require `python -m` prefix (uvicorn, pytest, playwright, safety, bandit)
   - FORBIDDEN: `uv pip install`, `@latest` syntax

2. Code Quality
   - Type hints required for all code
   - Follow existing patterns exactly
   - Use Google style for docstring

3. Testing Requirements
   - Framework: `uv run python -m pytest` (use python -m prefix)
   - Coverage: test edge cases and errors
   - New features require tests
   - Bug fixes require regression tests
   - E2E tests: Use function-scoped fixtures to avoid async deadlocks

4. Git
   - Follow the Conventional Commits style on commit messages.

## Framework Information

- **Current Framework**: FastAPI (migrated from Litestar)
- **Application Entry**: `src.app:app`
- **Key Endpoints**: 
  - `/` - Root endpoint
  - `/health` - Health check (required for CI/CD)
  - `/docs` - OpenAPI documentation
  - `/redoc` - ReDoc documentation

## Clean Architecture Structure

This project follows Clean Architecture principles with the following structure:

```
src/ph_stats/
├── domain/                 # Core business logic (innermost layer)
│   ├── entities/          # Business models and domain entities
│   └── repositories/      # Domain repository interfaces (contracts)
├── application/           # Use cases and orchestration
│   └── services/          # Application services and use case implementations
├── infrastructure/        # External implementations (outermost layer)
│   └── repositories/      # Database and external service implementations
├── api/                   # Presentation layer (HTTP/REST API)
├── utils/                 # Shared utilities
└── __init__.py
```

### Layer Responsibilities:

1. **Domain Layer** (`domain/`):
   - Contains core business logic and rules
   - Independent of external concerns (databases, frameworks, etc.)
   - Entities represent business concepts and rules
   - Repository interfaces define contracts for data access

2. **Application Layer** (`application/`):
   - Orchestrates domain objects to fulfill use cases
   - Contains application-specific business rules
   - Depends only on domain layer

3. **Infrastructure Layer** (`infrastructure/`):
   - Implements domain repository interfaces
   - Handles external concerns (database, APIs, file systems)
   - Contains framework-specific implementations

4. **API Layer** (`api/`):
   - Handles HTTP requests/responses
   - Contains FastAPI routes and controllers
   - Transforms between API models and domain models

### Import Guidelines:
- Domain layer should not import from other layers
- Application layer can import from domain
- Infrastructure layer can import from domain and application
- API layer can import from all layers
- Use clean dependency direction: API → Application → Domain ← Infrastructure

## Code Formatting and Linting

1. Ruff
   - Format: `uv run ruff format .`
   - Check: `uv run ruff check .`
   - Fix: `uv run ruff check . --fix`
2. Pre-commit
   - Config: `.pre-commit-config.yaml`
   - Runs: on git commit
   - Tools: Ruff (Python)

## Testing Strategy

1. Test Categories:
   - Unit tests: `tests/unit/` - Individual component testing
   - Integration tests: `tests/integration/` - Component interaction testing  
   - E2E tests: `tests/e2e/` - Full application workflow testing

2. Test Execution:
   - Unit: `uv run python -m pytest tests/unit/`
   - Integration: `uv run python -m pytest tests/integration/`
   - E2E: `uv run python -m pytest tests/e2e/`
   - All: `uv run python -m pytest`

3. Coverage Requirements:
   - Unit tests: ≥80% coverage required
   - Integration tests: Cover critical workflows
   - E2E tests: Cover user journeys and API endpoints

## E2E Testing

1. Framework: pytest + Playwright + httpx
   - Use function-scoped fixtures to avoid async deadlocks
   - Never use session-scoped async fixtures with pytest-asyncio
   - Test categories: @pytest.mark.e2e, @pytest.mark.api, @pytest.mark.browser

2. Running E2E Tests:
   - API tests only: `uv run python -m pytest tests/e2e -m "api" -v`
   - Browser tests: `uv run python -m pytest tests/e2e -m "browser" -v` 
   - All E2E: `uv run python -m pytest tests/e2e/ -v`

3. Troubleshooting:
   - If tests hang: Check fixture scopes (use function, not session for async)
   - Server startup issues: Verify uvicorn command uses `python -m uvicorn`

## Common Commands

### Development
- Start server: `uv run python -m uvicorn src.app:app --reload`
- Run linting: `uv run ruff check . --fix`
- Format code: `uv run ruff format .`

### Testing  
- All tests: `uv run python -m pytest`
- E2E only: `uv run python -m pytest tests/e2e/ -v`
- With coverage: `uv run python -m pytest --cov=src`

### Troubleshooting
- Check app health: `curl http://localhost:8000/health`
- Install browsers: `uv run python -m playwright install chromium`

## CI/CD Troubleshooting

1. Application Startup Issues:
   - Use `uv run python -m uvicorn src.app:app` (not just `uvicorn`)
   - Ensure /health endpoint exists for health checks
   - Check application entry point matches workflow (src.app:app)

2. E2E Test Failures:
   - Install Playwright browsers: `uv run python -m playwright install chromium`
   - Check fixture scopes in conftest.py
   - Verify server startup timeout settings

## Known Issues & Solutions

1. **E2E Tests Hanging**:
   - Cause: Session-scoped async fixtures
   - Solution: Use function-scoped fixtures in conftest.py

2. **uvicorn Command Not Found**:
   - Cause: UV doesn't expose direct command
   - Solution: Use `uv run python -m uvicorn`

3. **CI/CD Health Check Failing**:
   - Cause: Missing /health endpoint or wrong app path
   - Solution: Ensure /health endpoint exists, use correct src.app:app path

## Git Commands

Initialize and manage this project with git:

```bash
# Initialize repository (if not already done)
git init

# Add all files and make initial commit
git add .
git commit -m "feat: initial project setup with FastAPI framework"

# Set up remote repository (replace with your actual repo URL)
git remote add origin <your-repo-url>
git push -u origin main
```

## Branch Strategy

### Main Branch Protection
- `main` branch should always be stable and deployable
- Direct commits to `main` are discouraged
- Use pull requests for code review

### Feature Branch Workflow
```bash
# Create and switch to feature branch
git checkout -b feature/your-feature-name

# Work on your feature, commit regularly
git add .
git commit -m "feat: implement user authentication"

# Push feature branch
git push -u origin feature/your-feature-name

# When ready, merge back to main (after PR review)
git checkout main
git pull origin main
git merge feature/your-feature-name
git push origin main

# Clean up feature branch
git branch -d feature/your-feature-name
git push origin --delete feature/your-feature-name
```

### Daily Workflow
```bash
# Start new work - always from updated main
git checkout main
git pull origin main
git checkout -b feature/new-feature

# Regular commits during development
git add .
git commit -m "feat: add new feature component"

# Push and create PR when ready
git push -u origin feature/new-feature
```

### Branch Naming Conventions
- `feature/description` - New features
- `fix/description` - Bug fixes  
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
