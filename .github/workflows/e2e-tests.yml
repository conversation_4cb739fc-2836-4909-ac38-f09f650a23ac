name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.12"
  UV_CACHE_DIR: /tmp/.uv-cache

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
      
      - name: Restore uv cache
        uses: actions/cache@v4
        with:
          path: /tmp/.uv-cache
          key: uv-${{ runner.os }}-${{ hashFiles('uv.lock') }}
          restore-keys: |
            uv-${{ runner.os }}-${{ hashFiles('uv.lock') }}
            uv-${{ runner.os }}
      
      - name: Install dependencies
        run: uv sync --all-extras --dev
      
      - name: Set up test environment
        env:
          TEST_DATABASE_URL: postgresql+asyncpg://postgres:testpassword@localhost:5432/testdb
          SECRET_KEY: test-secret-key-for-ci
          SUPABASE_URL: http://localhost:54321
          SUPABASE_ANON_KEY: test-anon-key
          SUPABASE_SERVICE_ROLE_KEY: test-service-role-key
        run: |
          echo "TEST_DATABASE_URL=$TEST_DATABASE_URL" >> $GITHUB_ENV
          echo "SECRET_KEY=$SECRET_KEY" >> $GITHUB_ENV
          echo "SUPABASE_URL=$SUPABASE_URL" >> $GITHUB_ENV
          echo "SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY" >> $GITHUB_ENV
          echo "SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY" >> $GITHUB_ENV
      
      - name: Install Playwright browsers
        run: uv run playwright install --with-deps chromium firefox
      
      - name: Run E2E tests
        run: uv run python -m pytest tests/e2e -v
        env:
          TEST_DATABASE_URL: postgresql+asyncpg://postgres:testpassword@localhost:5432/testdb
          SECRET_KEY: test-secret-key-for-ci
          SUPABASE_URL: http://localhost:54321
          SUPABASE_ANON_KEY: test-anon-key
          SUPABASE_SERVICE_ROLE_KEY: test-service-role-key
      
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            test-results/
            playwright-report/
      
      - name: Minimize uv cache
        run: uv cache prune --ci