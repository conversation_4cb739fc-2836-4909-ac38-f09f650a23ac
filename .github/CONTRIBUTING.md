# Contributing to Ph Stats FastAPI

Thank you for your interest in contributing to Ph Stats FastAPI! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Making Changes](#making-changes)
- [Testing](#testing)
- [Submitting Changes](#submitting-changes)
- [Code Review Process](#code-review-process)
- [Style Guidelines](#style-guidelines)
- [Release Process](#release-process)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. We are committed to providing a welcoming and inclusive environment for all contributors.

### Our Standards

- Use welcoming and inclusive language
- Be respectful of differing viewpoints and experiences
- Gracefully accept constructive criticism
- Focus on what is best for the community
- Show empathy towards other community members

## Getting Started

### Prerequisites

- Python 3.12 or higher
- uv package manager
- Git
- Supabase account (for database features)

### Development Setup

1. **Fork and Clone the Repository**
   ```bash
   git clone https://github.com/yourusername/ph-stats-fastapi.git
   cd ph-stats-fastapi
   ```

2. **Set Up Development Environment**
   ```bash
   # Install dependencies
   uv sync --all-extras --dev
   
   # Set up environment variables
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

3. **Verify Setup**
   ```bash
   # Run tests to ensure everything works
   uv run pytest tests/unit -v
   
   # Run linting
   uv run ruff check .
   uv run ruff format . --check
   ```

4. **Set Up Pre-commit Hooks** (Optional but Recommended)
   ```bash
   uv run pre-commit install
   ```

## Making Changes

### Branch Strategy

We use Git Flow branching strategy:

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/description` - New features
- `fix/description` - Bug fixes
- `hotfix/description` - Critical production fixes

### Creating a Feature Branch

```bash
# Start from develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name

# Make your changes and commit
git add .
git commit -m "feat: add your feature description"

# Push to your fork
git push origin feature/your-feature-name
```

### Commit Message Convention

We follow [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(api): add user statistics endpoint
fix(auth): resolve JWT token validation issue
docs: update API documentation for statistics
test: add integration tests for user management
```

## Testing

### Test Structure

```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── e2e/           # End-to-end tests
├── factories/     # Test data factories
└── fixtures/      # Test fixtures
```

### Running Tests

```bash
# Run all tests
uv run pytest

# Run specific test types
uv run pytest tests/unit -v
uv run pytest tests/integration -v
uv run pytest tests/e2e -v

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run tests in parallel
uv run pytest -n auto
```

### Test Requirements

- **Unit Tests**: Required for all new functions and classes
- **Integration Tests**: Required for API endpoints and database interactions
- **E2E Tests**: Required for user-facing features
- **Coverage**: Minimum 90% code coverage for new code

### Writing Tests

```python
# Unit test example
import pytest
from src.ph_stats.application.services.user_service import UserService

@pytest.mark.asyncio
async def test_create_user_success():
    """Test successful user creation."""
    service = UserService()
    user_data = {"email": "<EMAIL>", "username": "testuser"}
    
    user = await service.create_user(user_data)
    
    assert user.email == "<EMAIL>"
    assert user.username == "testuser"

# Integration test example
@pytest.mark.asyncio
async def test_user_registration_endpoint(client):
    """Test user registration API endpoint."""
    response = await client.post("/api/v1/auth/register", json={
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "securepassword123"
    })
    
    assert response.status_code == 201
    assert "access_token" in response.json()
```

## Submitting Changes

### Pull Request Process

1. **Create Pull Request**
   - Use the PR template provided
   - Fill out all relevant sections
   - Link related issues

2. **PR Requirements**
   - All tests must pass
   - Code coverage must not decrease
   - Code must pass linting checks
   - Documentation must be updated if needed

3. **Review Process**
   - At least one code review required
   - Address all review comments
   - Ensure CI/CD pipeline passes

### Pull Request Checklist

- [ ] Tests added/updated for new functionality
- [ ] Documentation updated if needed
- [ ] Code follows project style guidelines
- [ ] All CI checks pass
- [ ] Self-review completed
- [ ] Breaking changes documented

## Code Review Process

### For Contributors

- Respond to review comments promptly
- Be open to feedback and suggestions
- Make requested changes or explain why they're not needed
- Update the PR description if the scope changes

### For Reviewers

- Provide constructive feedback
- Focus on code quality, security, and maintainability
- Approve when requirements are met
- Suggest alternatives when possible

## Style Guidelines

### Python Code Style

We use Ruff for linting and formatting:

```bash
# Format code
uv run ruff format .

# Check for issues
uv run ruff check .

# Fix auto-fixable issues
uv run ruff check . --fix
```

### Key Guidelines

- Follow PEP 8 style guide
- Use type hints for all functions
- Write docstrings for public functions
- Keep functions focused and small
- Use meaningful variable names

```python
# Good
async def create_user_statistic(
    user_id: int,
    metric_name: str,
    metric_value: float,
    tags: dict[str, str] | None = None
) -> UserStatistic:
    """Create a new user statistic record.
    
    Args:
        user_id: The ID of the user
        metric_name: Name of the metric being recorded
        metric_value: Numeric value of the metric
        tags: Optional metadata tags
        
    Returns:
        The created UserStatistic instance
        
    Raises:
        ValidationError: If metric data is invalid
    """
    if tags is None:
        tags = {}
    
    # Implementation here
    pass
```

### API Design Guidelines

- Use RESTful conventions
- Include proper HTTP status codes
- Provide consistent error responses
- Version your APIs (e.g., `/api/v1/`)

### Database Guidelines

- Use migrations for all schema changes
- Include proper indexes
- Use Row Level Security (RLS) policies
- Follow naming conventions

## Release Process

### Versioning

We use [Semantic Versioning](https://semver.org/):

- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Release Workflow

1. Feature complete on `develop`
2. Create release branch: `release/v1.0.0`
3. Final testing and bug fixes
4. Merge to `main` and tag
5. Deploy to production
6. Merge back to `develop`

## Getting Help

- **Documentation**: Check our [docs](docs/README.md) first
- **Discussions**: Use GitHub Discussions for questions
- **Issues**: Create an issue for bugs or feature requests
- **Discord**: Join our community Discord (link in README)

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to Ph Stats FastAPI! 🚀