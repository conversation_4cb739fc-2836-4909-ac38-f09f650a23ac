name: ✨ Feature Request
description: Suggest an idea for Ph Stats FastAPI
title: "[Feature]: "
labels: ["enhancement", "feature-request"]
assignees:
  - 
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a new feature! Please provide as much detail as possible.

  - type: input
    id: contact
    attributes:
      label: Contact Details
      description: How can we get in touch with you if we need more info?
      placeholder: ex. <EMAIL>
    validations:
      required: false

  - type: textarea
    id: problem-statement
    attributes:
      label: Problem Statement
      description: Is your feature request related to a problem? Please describe the problem you're trying to solve.
      placeholder: I'm always frustrated when...
    validations:
      required: true

  - type: textarea
    id: proposed-solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented.
      placeholder: I would like to see...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered.
      placeholder: I have considered...
    validations:
      required: false

  - type: dropdown
    id: feature-type
    attributes:
      label: Feature Type
      description: What type of feature is this?
      options:
        - User Interface Enhancement
        - API Enhancement
        - Performance Improvement
        - Security Enhancement
        - Developer Experience
        - Documentation
        - Testing
        - Other
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my use case
        - Critical - Blocking my use case
    validations:
      required: true

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe your specific use case and how this feature would help.
      placeholder: In my workflow, I need to...
    validations:
      required: true

  - type: textarea
    id: acceptance-criteria
    attributes:
      label: Acceptance Criteria
      description: What would need to be implemented for this feature to be considered complete?
      placeholder: |
        - [ ] User can do X
        - [ ] System responds with Y
        - [ ] Error handling for Z
    validations:
      required: false

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context, mockups, or examples about the feature request here.