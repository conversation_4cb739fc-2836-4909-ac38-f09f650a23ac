# Testing Guide

This document provides comprehensive guidance on testing in the Powerhouse Stats FastAPI project.

## Overview

Our testing strategy follows a three-tier approach:
- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing  
- **E2E Tests**: Full application workflow testing

## Test Structure

```
tests/
├── unit/              # Unit tests for individual components
├── integration/       # Integration tests for component interactions
├── e2e/              # End-to-end tests with Playwright
├── factories/        # Test data factories
├── fixtures/         # Common test fixtures
└── conftest.py       # Global test configuration
```

## Unit Testing

### Purpose
- Test individual functions, classes, and modules in isolation
- Fast execution and reliable feedback
- High coverage of edge cases and error conditions

### Framework
- **pytest** - Main testing framework
- **pytest-asyncio** - Async test support
- **pytest-mock** - Mocking capabilities

### Running Unit Tests
```bash
# Run all unit tests
uv run python -m pytest tests/unit/ -v

# Run with coverage
uv run python -m pytest tests/unit/ --cov=src --cov-report=html

# Run specific test file
uv run python -m pytest tests/unit/test_example.py -v
```

### Best Practices
- Use descriptive test names that explain the scenario
- Follow AAA pattern: Arrange, Act, Assert
- Test both success and failure scenarios
- Mock external dependencies
- Aim for ≥80% code coverage

### Example Unit Test
```python
import pytest
from src.ph_stats.application.services.calculator import Calculator

class TestCalculator:
    """Test Calculator service."""

    def test_add_positive_numbers(self):
        """Test adding two positive numbers."""
        # Arrange
        calc = Calculator()
        
        # Act
        result = calc.add(2, 3)
        
        # Assert
        assert result == 5

    def test_add_with_zero(self):
        """Test adding zero to a number."""
        calc = Calculator()
        assert calc.add(5, 0) == 5
        assert calc.add(0, 5) == 5

    def test_divide_by_zero_raises_error(self):
        """Test division by zero raises appropriate error."""
        calc = Calculator()
        with pytest.raises(ValueError, match="Cannot divide by zero"):
            calc.divide(10, 0)
```

## Integration Testing

### Purpose
- Test interaction between multiple components
- Verify database operations and API integrations
- Test service layer interactions

### Running Integration Tests
```bash
# Run all integration tests
uv run python -m pytest tests/integration/ -v

# Run with database setup
uv run python -m pytest tests/integration/ -v --setup-database
```

### Database Testing
- Use test database (separate from development/production)
- Implement database fixtures for setup/teardown
- Test database migrations and schema changes

### Example Integration Test
```python
import pytest
from sqlmodel import Session
from src.ph_stats.infrastructure.repositories.session import get_session
from src.ph_stats.domain.entities.user import User
from src.ph_stats.application.services.user_service import UserService

class TestUserService:
    """Test UserService with database integration."""

    async def test_create_user(self, test_session: Session):
        """Test creating a new user."""
        # Arrange
        user_service = UserService(test_session)
        user_data = {"name": "John Doe", "email": "<EMAIL>"}
        
        # Act
        created_user = await user_service.create_user(user_data)
        
        # Assert
        assert created_user.name == "John Doe"
        assert created_user.email == "<EMAIL>"
        assert created_user.id is not None
```

## E2E Testing

### Purpose
- Test complete user workflows from UI/API perspective
- Validate system behavior in realistic scenarios
- Ensure all components work together correctly

### Framework
- **pytest** - Test framework
- **Playwright** - Browser automation
- **httpx** - HTTP client for API testing

### Test Categories
Use pytest markers to categorize E2E tests:

```python
@pytest.mark.e2e          # All end-to-end tests
@pytest.mark.api          # API endpoint tests
@pytest.mark.browser      # Browser automation tests
@pytest.mark.slow         # Tests that take longer to execute
```

### Running E2E Tests
```bash
# Run all E2E tests
uv run python -m pytest tests/e2e/ -v

# Run only API tests (faster)
uv run python -m pytest tests/e2e -m "api" -v

# Run only browser tests
uv run python -m pytest tests/e2e -m "browser" -v

# Run specific test class
uv run python -m pytest tests/e2e/test_health_check.py::TestHealthCheck -v
```

### Browser Setup
Install Playwright browsers before running browser tests:
```bash
uv run python -m playwright install chromium firefox
```

### E2E Test Configuration

The E2E tests use function-scoped fixtures to prevent async deadlocks:

```python
# conftest.py
@pytest.fixture(scope="function")
async def playwright() -> Playwright:
    """Start Playwright for each test function."""
    async with async_playwright() as p:
        yield p

@pytest.fixture(scope="function")
async def browser(playwright: Playwright) -> Browser:
    """Launch browser for each test."""
    browser = await playwright.chromium.launch(headless=True)
    yield browser
    await browser.close()
```

### Example E2E Tests
```python
import pytest
import httpx
from playwright.async_api import Page, expect

@pytest.mark.e2e
class TestHealthEndpoints:
    """Test application health endpoints."""

    @pytest.mark.api
    async def test_health_api(self, http_client: httpx.AsyncClient):
        """Test health endpoint via API."""
        response = await http_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

    @pytest.mark.browser
    async def test_health_browser(self, page: Page, app_server: str):
        """Test health endpoint via browser."""
        await page.goto(f"{app_server}/health")
        content = await page.content()
        assert "healthy" in content

    @pytest.mark.browser
    async def test_openapi_docs(self, page: Page, app_server: str):
        """Test OpenAPI documentation loads."""
        page.set_default_timeout(10000)
        await page.goto(f"{app_server}/docs", wait_until="networkidle")
        await expect(page.locator("text=FastAPI")).to_be_visible()
```

## Test Data Management

### Factories
Use factories for creating test data:

```python
# tests/factories/user_factory.py
import factory
from src.ph_stats.domain.entities.user import User

class UserFactory(factory.Factory):
    class Meta:
        model = User

    name = factory.Faker("name")
    email = factory.Faker("email")
    is_active = True
```

### Fixtures
Create reusable fixtures for common test setup:

```python
# tests/conftest.py
@pytest.fixture
async def test_user(test_session):
    """Create a test user."""
    user = UserFactory()
    test_session.add(user)
    await test_session.commit()
    return user
```

## Performance Testing

### Load Testing with Locust
For performance testing, use Locust (included in dev dependencies):

```bash
# Run load tests
uv run locust -f tests/performance/locustfile.py --host=http://localhost:8000
```

### Example Locust Test
```python
from locust import HttpUser, task, between

class APIUser(HttpUser):
    wait_time = between(1, 3)

    @task
    def test_health_endpoint(self):
        self.client.get("/health")

    @task
    def test_root_endpoint(self):
        self.client.get("/")
```

## Troubleshooting

### Common Issues

1. **E2E Tests Hanging**
   - **Cause**: Session-scoped async fixtures
   - **Solution**: Use function-scoped fixtures in conftest.py

2. **Server Startup Failures**
   - **Cause**: Port conflicts or incorrect uvicorn command
   - **Solution**: Use `python -m uvicorn` and check port availability

3. **Playwright Browser Issues**
   - **Cause**: Missing browser binaries
   - **Solution**: Run `uv run python -m playwright install chromium`

4. **Database Connection Errors**
   - **Cause**: Test database not configured
   - **Solution**: Set up test database environment variables

### Debugging Tests
```bash
# Run tests with verbose output
uv run python -m pytest tests/ -v -s

# Run tests with debugging
uv run python -m pytest tests/ --pdb

# Run tests and show locals on failure
uv run python -m pytest tests/ -l --tb=long
```

### Test Environment Variables
Set up test-specific environment variables:

```bash
# .env.test
TEST_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/testdb
SECRET_KEY=test-secret-key
DEBUG=True
```

## CI/CD Integration

### GitHub Actions
Tests run automatically in CI/CD pipeline:

```yaml
# .github/workflows/ci.yml
- name: Run unit tests
  run: uv run pytest tests/unit -v --cov=src

- name: Run integration tests  
  run: uv run pytest tests/integration -v

- name: Run E2E tests
  run: uv run pytest tests/e2e -v
```

### Test Requirements
- All tests must pass before merge
- Maintain ≥80% code coverage
- E2E tests validate critical user paths
- Performance tests ensure response time requirements

## Best Practices Summary

1. **Write Tests First**: Use TDD approach when possible
2. **Test Pyramid**: More unit tests, fewer integration, minimal E2E
3. **Independent Tests**: Each test should run independently
4. **Clear Test Names**: Use descriptive names that explain the scenario
5. **Fast Feedback**: Keep unit tests fast (<1s each)
6. **Realistic Data**: Use factories for generating realistic test data
7. **Environment Isolation**: Use separate test environments
8. **Continuous Testing**: Run tests frequently during development
9. **Documentation**: Document complex test scenarios and setup requirements
10. **Regular Maintenance**: Keep tests up-to-date with code changes