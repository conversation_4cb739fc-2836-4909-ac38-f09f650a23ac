# System Architecture

## Architectural Overview

Ph Stats FastAPI follows a layered architecture pattern that promotes separation of concerns, testability, and maintainability. The system is designed as a modern web application with async-first principles and type safety throughout the stack.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Web Browser    │  Mobile App     │  API Clients               │
│  (HTMX + CSS)   │  (Future)       │  (External Systems)        │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Presentation Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  FastAPI Routes │  HTMX Endpoints │  API Endpoints             │
│  • Authentication • Component APIs • RESTful APIs              │
│  • Page Rendering • Real-time UI  • External Integration       │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Business Services │ Domain Logic    │ Use Cases                │
│  • UserService     • Validation     • Authentication           │
│  • StatsService    • Business Rules • Data Processing          │
│  • AuthService     • Calculations   • Workflows               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  Database       │  External APIs   │  File Storage             │
│  • PostgreSQL   • Email Service   • Static Files              │
│  • Redis Cache  • Monitoring      • Upload Storage            │
│  • Migrations   • Logging         • Backup Systems            │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Presentation Layer

#### FastAPI Application
- **Purpose**: HTTP request handling and response generation
- **Responsibilities**: 
  - Route management and URL dispatching
  - Request validation and serialization
  - Response formatting and status codes
  - API documentation generation

#### HTMX Integration
- **Purpose**: Dynamic frontend interactions without complex JavaScript
- **Components**:
  - Component endpoints for partial page updates
  - Real-time polling for live data updates
  - Progressive enhancement for accessibility
  - Form handling with server-side validation

#### Template System
- **Engine**: Jinja2 with JinjaX for server-side rendering
- **Structure**: Hierarchical template inheritance with component composition
- **Styling**: Tailwind CSS utility-first framework
- **Components**: Tailwind Plus UI blocks for pre-built interface elements
- **Features**: Component-based UI architecture with atomic design principles

### 2. Application Layer

#### Service Classes
Services encapsulate business logic and coordinate between the presentation and infrastructure layers.

**UserService**
```python
class UserService:
    async def register_user(self, user_data: UserCreate) -> User
    async def authenticate_user(self, credentials: UserLogin) -> User
    async def update_profile(self, user_id: UUID, updates: UserUpdate) -> User
    async def deactivate_user(self, user_id: UUID) -> bool
```

**StatsService**
```python
class StatsService:
    async def record_metric(self, metric_data: MetricCreate) -> Metric
    async def get_statistics(self, filters: StatsFilter) -> List[Statistic]
    async def calculate_aggregates(self, metric_name: str) -> AggregateResult
    async def generate_report(self, report_params: ReportParams) -> Report
```

**AuthService**
```python
class AuthService:
    def hash_password(self, password: str) -> str
    def verify_password(self, password: str, hash: str) -> bool
    def create_access_token(self, user_data: dict) -> str
    def verify_token(self, token: str) -> dict
```

### 3. Domain Layer

#### Data Models (SQLModel)
SQLModel provides type-safe data models that serve both as Pydantic schemas and SQLAlchemy ORM models.

```python
class User(UserBase, table=True):
    __tablename__ = "users"
    
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    hashed_password: str
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
    
    # Relationships
    statistics: List["UserStatistic"] = Relationship(back_populates="user")

class UserStatistic(StatisticBase, table=True):
    __tablename__ = "user_statistics"
    
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="users.id")
    metric_name: str = Field(index=True)
    metric_value: float
    recorded_at: datetime = Field(default_factory=datetime.utcnow)
    tags: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Relationships
    user: User = Relationship(back_populates="statistics")
```

### 4. Infrastructure Layer

#### Database Configuration
- **Primary Database**: Supabase PostgreSQL with built-in authentication and real-time features
- **Cache Layer**: Redis for session storage and frequently accessed data  
- **Connection Management**: Async connection pooling with SQLAlchemy to Supabase
- **Migration Management**: Supabase migrations with optional Alembic integration
- **Built-in Features**: Row Level Security (RLS), real-time subscriptions, edge functions

#### External Services
- **Database Service**: Supabase managed PostgreSQL with built-in APIs
- **Authentication**: Supabase Auth with JWT tokens and social providers
- **Real-time**: Supabase real-time subscriptions for live updates
- **Storage**: Supabase Storage for file uploads and static assets
- **Email Service**: Supabase Auth emails or external SMTP integration
- **Monitoring**: Render.com metrics and Supabase dashboard analytics
- **CDN**: Tailwind CSS from CDN or compiled assets via Render.com static hosting

## Data Flow Architecture

### Request Processing Flow

```
1. HTTP Request → FastAPI Router
2. Router → Dependency Injection (Auth, DB Session)
3. Route Handler → Service Layer
4. Service → Domain Logic + Data Validation
5. Service → Repository/Database Access
6. Database → Query Execution
7. Response ← Service Layer Processing
8. Response ← Template Rendering (HTMX + Tailwind CSS)
9. HTTP Response ← FastAPI Serialization
```

### Frontend Styling Architecture

```
1. Tailwind CSS Utility Classes → Component Styling
2. Tailwind Plus UI Blocks → Pre-built Components
3. JinjaX Components → Reusable Template Components
4. HTMX Interactions → Dynamic State Management
5. Responsive Design → Mobile-first Approach
```

### HTMX Interaction Flow

```
1. User Interaction → HTMX Trigger
2. HTMX → Async HTTP Request
3. FastAPI → HTMX-specific Endpoint
4. Service Layer → Business Logic
5. Template Rendering → Partial HTML with Tailwind CSS classes
6. HTMX ← Styled HTML Fragment Response
7. DOM Update → Seamless UI Update with animations
```

## UI/UX Architecture

### Design System Foundation
- **Utility-First**: Tailwind CSS provides low-level utility classes
- **Component Library**: Tailwind Plus UI blocks for consistent design patterns
- **Design Tokens**: Consistent spacing, colors, typography through Tailwind config
- **Responsive Design**: Mobile-first approach with Tailwind's responsive utilities

### Component Hierarchy
```
Design System Layer:
├── Tailwind CSS Base (reset, utilities)
├── Custom Tailwind Config (colors, spacing, typography)
├── Tailwind Plus UI Blocks (pre-built components)
└── JinjaX Components (app-specific components)

Template Layer:
├── Base Templates (layout, navigation)
├── Page Templates (dashboard, auth, stats)
├── Component Templates (forms, cards, modals)
└── Partial Templates (HTMX fragments)
```

### Styling Strategy
- **Atomic Design**: Build complex interfaces from simple, reusable components
- **Utility Classes**: Direct styling in templates for rapid development
- **Component Variants**: JinjaX components with Tailwind variant classes
- **State Management**: HTMX attributes with Tailwind state utilities (hover, focus, active)
- **Accessibility**: Built-in focus management and ARIA attributes

### Performance Optimization
- **CSS Purging**: Tailwind's purge removes unused styles in production
- **CDN Delivery**: Tailwind CSS served from fast CDN
- **Critical CSS**: Above-the-fold styles inlined for faster rendering
- **Component Caching**: JinjaX component output caching

## Security Architecture

### Authentication & Authorization
- **Supabase Auth**: Built-in authentication with JWT tokens and refresh tokens
- **Social Providers**: Optional Google, GitHub, Discord authentication via Supabase
- **Row Level Security**: Database-level access control with Supabase RLS policies
- **Role-Based Access**: Custom roles with Supabase Auth metadata
- **Session Management**: Supabase session handling with optional Redis caching

### Data Protection
- **Input Validation**: Pydantic schema validation at API boundaries
- **SQL Injection Prevention**: SQLAlchemy ORM with parameterized queries
- **XSS Protection**: Template auto-escaping and CSP headers
- **CSRF Protection**: Token-based CSRF prevention for forms

### Security Headers
```python
security_headers = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'"
}
```

## Performance Architecture

### Async Processing
- **Async/Await**: Full async implementation for I/O operations
- **Connection Pooling**: Database connection reuse
- **Background Tasks**: Non-blocking task processing
- **Streaming Responses**: Large data efficient transfer

### Caching Strategy
```python
# Multi-layer caching approach
1. Application Cache → Redis (frequently accessed data)
2. Query Cache → SQLAlchemy query result caching
3. HTTP Cache → Browser caching with appropriate headers
4. CDN Cache → Static asset distribution (future)
```

### Database Optimization
- **Indexing Strategy**: Optimized indexes for query patterns
- **Query Optimization**: Efficient SQLAlchemy queries
- **Connection Management**: Async connection pooling
- **Read Replicas**: Future scaling with read/write separation

## Scalability Design

### Horizontal Scaling
- **Stateless Services**: Services designed for horizontal scaling
- **Load Balancing**: Multiple application instances
- **Database Scaling**: Connection pooling and query optimization
- **Cache Distribution**: Redis clustering for cache scaling

### Vertical Scaling
- **Resource Optimization**: Efficient memory and CPU usage
- **Database Tuning**: PostgreSQL performance optimization
- **Application Profiling**: Continuous performance monitoring

## Deployment Architecture

### Render.com Cloud Deployment
```bash
# Production deployment on Render.com platform
# Web service with automatic deployments from Git
# Environment variables managed through Render dashboard
# Built-in SSL, CDN, and scaling capabilities
```

### Infrastructure Management
- **Web Service**: Render.com web service with automatic deployments
- **Database**: Supabase managed PostgreSQL with global distribution
- **Caching**: Render.com Redis addon for session and data caching
- **Static Files**: Render.com static site or Supabase Storage for assets
- **Process Management**: Render.com automatic process management and health checks
- **Environment Management**: Render.com environment variables and Supabase project settings

### Monitoring & Observability
- **Application Monitoring**: Render.com built-in metrics and logging
- **Database Analytics**: Supabase dashboard for database performance and usage
- **Real-time Monitoring**: Supabase real-time connection and subscription metrics
- **Health Checks**: Render.com automatic health checks and uptime monitoring
- **Error Tracking**: Application logs via Render.com with optional external services
- **Performance Metrics**: Response times, database queries, and resource usage

## Technology Decisions

### Framework Selection Rationale

**FastAPI**
- High performance with automatic API documentation
- Native async support for high concurrency
- Excellent type hints integration
- Rich ecosystem and active development

**SQLModel**
- Type safety across data layer
- Single model definition for API and database
- Leverages Pydantic validation
- Modern SQLAlchemy integration

**HTMX**
- Progressive enhancement without complex JavaScript
- Server-side rendering with dynamic interactions
- Reduced frontend complexity
- Excellent accessibility support

**Supabase (PostgreSQL)**
- Managed PostgreSQL with ACID compliance and advanced features
- Built-in authentication, real-time subscriptions, and APIs
- Row Level Security for fine-grained access control
- Global edge distribution and automatic scaling
- Integrated file storage and edge functions
- Excellent Python integration with official client libraries

## Future Architecture Considerations

### Microservices Evolution
- **Service Boundaries**: Clear domain separation prepared for microservices
- **API Design**: RESTful APIs ready for service extraction
- **Data Isolation**: Domain-specific data models

### Event-Driven Architecture
- **Event Sourcing**: Consideration for audit trails and historical data
- **Message Queues**: Async processing for heavy operations
- **Real-time Notifications**: WebSocket integration for instant updates

### Cloud-Native Features
- **Auto-scaling**: Render.com automatic scaling based on traffic and resource usage
- **Edge Distribution**: Supabase global edge network for database and storage
- **CDN Integration**: Render.com built-in CDN for static assets and caching
- **Serverless Functions**: Supabase Edge Functions for custom server-side logic
- **Real-time Infrastructure**: Native WebSocket support via Supabase real-time
- **Observability**: Integrated monitoring across Render.com and Supabase platforms

---

*This architecture documentation provides the foundation for understanding the Ph Stats FastAPI system design. For implementation details, refer to the development documentation in the `dev/` directory.*