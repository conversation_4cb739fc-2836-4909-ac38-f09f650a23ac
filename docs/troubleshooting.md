# Troubleshooting Guide

This document provides solutions to common issues encountered during development of the Powerhouse Stats FastAPI project.

## Table of Contents

- [Application Startup Issues](#application-startup-issues)
- [Testing Problems](#testing-problems)
- [CI/CD Pipeline Issues](#cicd-pipeline-issues)
- [Database Connection Issues](#database-connection-issues)
- [Package Management Issues](#package-management-issues)
- [Performance Issues](#performance-issues)
- [Browser Automation Issues](#browser-automation-issues)

## Application Startup Issues

### Issue: `uvicorn` command not found

**Symptoms:**
```bash
$ uv run uvicorn src.app:app
error: Failed to spawn: `uvicorn`
Caused by: No such file or directory (os error 2)
```

**Cause:** UV doesn't expose uvicorn as a direct command in some environments.

**Solution:**
Use the Python module syntax:
```bash
uv run python -m uvicorn src.app:app --host 0.0.0.0 --port 8000 --reload
```

### Issue: Application fails to start with import errors

**Symptoms:**
```bash
ModuleNotFoundError: No module named 'src'
```

**Cause:** Python path configuration or incorrect working directory.

**Solutions:**
1. Ensure you're in the project root directory
2. Check `pyproject.toml` has correct `pythonpath = ["."]`
3. Verify application structure matches expected paths

### Issue: Health endpoint returns 404

**Symptoms:**
- CI/CD health checks fail
- `/health` endpoint not accessible

**Cause:** Missing health endpoint in application.

**Solution:**
Ensure the health endpoint is defined in `src/app.py`:
```python
@app.get("/health")
async def health() -> dict[str, str]:
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "ph-stats-fastapi"}
```

## Testing Problems

### Issue: E2E tests hanging indefinitely

**Symptoms:**
- Tests start but never complete
- Process needs to be killed manually
- No error messages displayed

**Cause:** Session-scoped async fixtures causing deadlocks with pytest-asyncio.

**Solution:**
Use function-scoped fixtures in `tests/e2e/conftest.py`:
```python
@pytest.fixture(scope="function")  # Not "session"
async def playwright() -> Playwright:
    async with async_playwright() as p:
        yield p

@pytest.fixture(scope="function")  # Not "session"  
async def browser(playwright: Playwright) -> Browser:
    browser = await playwright.chromium.launch(headless=True)
    yield browser
    await browser.close()
```

**Additional fixes:**
- Remove custom `event_loop` fixture when using pytest-asyncio auto mode
- Set timeouts on Playwright operations:
```python
page.set_default_timeout(10000)  # 10 seconds
await page.goto(url, timeout=15000)
```

### Issue: `pytest` command not found

**Symptoms:**
```bash
$ uv run pytest
error: Failed to spawn: `pytest`
```

**Solution:**
Use Python module syntax:
```bash
uv run python -m pytest tests/ -v
```

### Issue: Playwright browser tests fail

**Symptoms:**
```bash
playwright._impl._api_types.Error: Executable doesn't exist
```

**Cause:** Playwright browsers not installed.

**Solution:**
Install required browsers:
```bash
uv run python -m playwright install chromium firefox
```

For CI/CD, ensure browsers are installed in workflow:
```yaml
- name: Install Playwright browsers
  run: uv run python -m playwright install --with-deps chromium firefox
```

### Issue: Test server won't start

**Symptoms:**
- E2E tests fail with connection errors
- "Failed to start test server" error

**Causes and Solutions:**

1. **Port conflict:**
   ```python
   # Change port in conftest.py
   ["uv", "run", "python", "-m", "uvicorn", "src.app:app", "--port", "8001"]
   ```

2. **Incorrect application path:**
   ```python
   # Ensure correct path in conftest.py
   ["uv", "run", "python", "-m", "uvicorn", "src.app:app", ...]
   ```

3. **Health check timeout:**
   ```python
   # Increase retries in conftest.py
   max_retries = 60  # Instead of 30
   ```

## CI/CD Pipeline Issues

### Issue: GitHub Actions workflow fails on application startup

**Symptoms:**
- E2E tests fail in CI but pass locally
- Health check failures in workflow

**Solutions:**

1. **Update workflow to use correct uvicorn command:**
   ```yaml
   - name: Start application in background
     run: |
       uv run python -m uvicorn src.app:app --host 0.0.0.0 --port 8000 &
       sleep 10
       curl -f http://localhost:8000/health
   ```

2. **Ensure health endpoint exists and is accessible**

3. **Add proper environment variables:**
   ```yaml
   env:
     TEST_DATABASE_URL: postgresql+asyncpg://postgres:testpassword@localhost:5432/testdb
     SECRET_KEY: test-secret-key-for-ci
   ```

### Issue: Tests pass locally but fail in CI

**Common causes and solutions:**

1. **Environment differences:**
   - Ensure same Python version
   - Check dependency versions match
   - Verify environment variables are set

2. **Timing issues:**
   - Increase timeouts in CI environment
   - Add retry logic for flaky operations
   - Use proper wait conditions

3. **Resource constraints:**
   - Reduce parallel test execution
   - Add memory/CPU limits
   - Use headless browser mode

### Issue: Coverage reports not generated

**Symptoms:**
- Coverage data missing from CI
- codecov uploads fail

**Solution:**
Ensure coverage is collected properly:
```bash
uv run python -m pytest tests/ --cov=src --cov-report=xml --cov-report=term-missing
```

## Database Connection Issues

### Issue: Database connection refused

**Symptoms:**
```bash
sqlalchemy.exc.OperationalError: connection to server refused
```

**Solutions:**

1. **Check database service status:**
   ```bash
   # For local development
   pg_isready -h localhost -p 5432
   ```

2. **Verify connection string:**
   ```bash
   TEST_DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/testdb
   ```

3. **For CI/CD, ensure database service is configured:**
   ```yaml
   services:
     postgres:
       image: postgres:16
       env:
         POSTGRES_PASSWORD: testpassword
         POSTGRES_DB: testdb
       options: >-
         --health-cmd pg_isready
         --health-interval 10s
         --health-timeout 5s
         --health-retries 5
   ```

### Issue: Database migrations fail

**Symptoms:**
- Alembic migration errors
- Schema conflicts

**Solutions:**

1. **Reset test database:**
   ```bash
   # Drop and recreate test database
   dropdb testdb
   createdb testdb
   ```

2. **Run migrations explicitly:**
   ```bash
   uv run alembic upgrade head
   ```

3. **Check migration dependencies and conflicts**

## Package Management Issues

### Issue: Dependency conflicts

**Symptoms:**
```bash
uv: ResolutionImpossible: No solution found when resolving dependencies
```

**Solutions:**

1. **Update lock file:**
   ```bash
   rm uv.lock
   uv sync --all-extras --dev
   ```

2. **Check for conflicting version constraints in pyproject.toml**

3. **Use compatible versions:**
   ```bash
   uv add package --upgrade-package package
   ```

### Issue: Virtual environment conflicts

**Symptoms:**
```bash
warning: `VIRTUAL_ENV=/path/to/other/.venv` does not match project environment
```

**Solution:**
Ensure you're using the correct virtual environment:
```bash
# Activate correct environment
source .venv/bin/activate

# Or use uv run consistently
uv run python -c "import sys; print(sys.path)"
```

## Performance Issues

### Issue: Slow test execution

**Symptoms:**
- Tests take too long to complete
- CI/CD timeouts

**Solutions:**

1. **Use parallel test execution:**
   ```bash
   uv run python -m pytest tests/ -n auto
   ```

2. **Run only relevant tests:**
   ```bash
   uv run python -m pytest tests/unit/ -m "not slow"
   ```

3. **Optimize test setup:**
   - Use function-scoped fixtures only when necessary
   - Mock external dependencies
   - Use in-memory databases for testing

### Issue: High memory usage during testing

**Solutions:**

1. **Limit parallel processes:**
   ```bash
   uv run python -m pytest tests/ -n 2  # Instead of auto
   ```

2. **Clean up resources:**
   ```python
   # Ensure proper cleanup in fixtures
   @pytest.fixture
   async def resource():
       resource = create_resource()
       yield resource
       await resource.cleanup()
   ```

## Browser Automation Issues

### Issue: Playwright timeouts

**Symptoms:**
```bash
TimeoutError: page.goto: Timeout 30000ms exceeded
```

**Solutions:**

1. **Increase timeouts:**
   ```python
   page.set_default_timeout(30000)
   await page.goto(url, timeout=60000)
   ```

2. **Use proper wait conditions:**
   ```python
   await page.goto(url, wait_until="networkidle")
   await page.wait_for_selector("text=Expected content")
   ```

3. **Handle slow loading:**
   ```python
   await page.goto(url, wait_until="domcontentloaded")
   await expect(page.locator("text=Content")).to_be_visible(timeout=10000)
   ```

### Issue: Element not found

**Symptoms:**
```bash
Error: strict mode violation: locator resolved to multiple elements
```

**Solutions:**

1. **Use more specific selectors:**
   ```python
   # Instead of
   page.locator("button")
   # Use
   page.locator("button[data-testid='submit']")
   ```

2. **Wait for element to be available:**
   ```python
   await page.wait_for_selector("button")
   await page.locator("button").click()
   ```

## Debug Commands

### Application Debugging
```bash
# Check application health
curl -v http://localhost:8000/health

# Start with debug logging
uv run python -m uvicorn src.app:app --log-level debug

# Check database connection
uv run python -c "from src.ph_stats.infrastructure.repositories import engine; print(engine)"
```

### Test Debugging
```bash
# Run with verbose output
uv run python -m pytest tests/ -v -s

# Run single test with debugging
uv run python -m pytest tests/test_example.py::test_function -v -s --pdb

# Show test output
uv run python -m pytest tests/ --capture=no

# Run tests with coverage and show missing lines
uv run python -m pytest tests/ --cov=src --cov-report=term-missing
```

### Environment Debugging
```bash
# Check Python path
uv run python -c "import sys; print('\n'.join(sys.path))"

# Check installed packages
uv run python -m pip list

# Check environment variables
uv run python -c "import os; print(os.environ.get('DATABASE_URL'))"

# Verify uv configuration
uv info
```

## Getting Help

If you encounter issues not covered in this guide:

1. **Check logs:** Look at application and test logs for specific error messages
2. **Search issues:** Check the project's GitHub issues for similar problems
3. **Verify environment:** Ensure all dependencies and environment variables are correct
4. **Test isolation:** Try to reproduce the issue with minimal test cases
5. **Document findings:** Add new solutions to this troubleshooting guide

Remember to always include specific error messages, environment details, and steps to reproduce when reporting issues.