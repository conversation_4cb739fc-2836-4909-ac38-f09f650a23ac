# Test-Driven Development Workflow

## TDD Philosophy

Ph Stats FastAPI follows a rigorous Test-Driven Development approach that ensures code quality, design clarity, and maintainability. Our TDD methodology emphasizes writing tests before implementation, driving design decisions through test requirements.

## Core TDD Principles

### 1. Red-Green-Refactor Cycle
```
🔴 RED    → Write a failing test (specify behavior)
🟢 GREEN  → Write minimal code to pass (implement)
🔵 REFACTOR → Improve code quality (optimize)
📊 VERIFY  → Run full suite and validate (confirm)
```

### 2. Outside-In Development
We follow an outside-in approach that starts with user behavior and works inward:
1. **E2E Tests** - Define user journeys and acceptance criteria
2. **Integration Tests** - Specify API contracts and service interactions
3. **Unit Tests** - Implement internal logic and edge cases

### 3. Test Pyramid Structure
```
    E2E Tests           ← 10% (Slow, High Value)
   ╱           ╲
  Integration Tests     ← 20% (Medium Speed, Contract Validation)
 ╱               ╲
Unit Tests             ← 70% (Fast, Detailed Logic)
```

## TDD Workflow Process

### Phase 1: Epic-Level TDD (User Behavior)

Start with end-to-end tests that define complete user journeys.

**Example: User Registration Epic**

**🔴 RED Phase** - Define user story with failing E2E test:
```python
# tests/e2e/test_user_registration_flow.py
import pytest
from playwright.async_api import Page, expect

@pytest.mark.asyncio
async def test_complete_user_registration_flow(page: Page):
    """
    Epic: User can register, receive confirmation, and access dashboard
    
    This test defines the complete user journey and will fail initially
    as no registration system exists yet.
    """
    # Navigate to registration page
    await page.goto("/register")
    
    # Verify registration form is present
    await expect(page.locator('form[data-testid="registration-form"]')).to_be_visible()
    
    # Fill registration form
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="username"]', "newuser")
    await page.fill('input[name="full_name"]', "New User")
    await page.fill('input[name="password"]', "securepassword123")
    await page.fill('input[name="confirm_password"]', "securepassword123")
    
    # Submit form
    await page.click('button[type="submit"]')
    
    # Should show success message via HTMX
    await expect(page.locator('[data-testid="success-message"]')).to_contain_text(
        "Registration successful! Please check your email to confirm your account."
    )
    
    # Should be redirected to login page
    await expect(page).to_have_url("/login?registered=true")
    
    # User should be able to log in immediately (email confirmation optional)
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "securepassword123")
    await page.click('button[type="submit"]')
    
    # Should redirect to dashboard
    await expect(page).to_have_url("/dashboard")
    await expect(page.locator('h1[data-testid="welcome-message"]')).to_contain_text("Welcome, New User!")
    
    # Dashboard should show empty state for new user
    await expect(page.locator('[data-testid="stats-empty-state"]')).to_contain_text(
        "No statistics recorded yet. Start by adding your first metric!"
    )
```

**🟢 GREEN Phase** - Build minimal implementation to pass E2E test:
1. Create registration template with HTMX
2. Implement registration endpoint
3. Add user model and database
4. Create authentication system
5. Build dashboard template

**🔵 REFACTOR Phase** - Enhance implementation:
- Add comprehensive validation
- Implement email verification
- Add security measures
- Optimize performance

### Phase 2: Feature-Level TDD (API Contracts)

Write integration tests that define API behavior and contracts.

**🔴 RED Phase** - Define API contract with failing integration test:
```python
# tests/integration/api/test_user_registration_api.py
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

@pytest.mark.asyncio
async def test_user_registration_api_success(client: AsyncClient, db_session: AsyncSession):
    """
    Integration: User registration API creates user and returns token
    
    This test defines the API contract and will fail initially
    as the registration endpoint doesn't exist.
    """
    registration_data = {
        "email": "<EMAIL>",
        "username": "integrationuser",
        "full_name": "Integration User",
        "password": "securepassword123"
    }
    
    response = await client.post("/api/v1/auth/register", json=registration_data)
    
    # Should return 201 Created
    assert response.status_code == 201
    
    data = response.json()
    
    # Response should contain user data (no sensitive info)
    assert data["user"]["email"] == "<EMAIL>"
    assert data["user"]["username"] == "integrationuser"
    assert data["user"]["full_name"] == "Integration User"
    assert "password" not in data["user"]
    assert "hashed_password" not in data["user"]
    assert "id" in data["user"]
    assert "created_at" in data["user"]
    
    # Should return authentication token
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data
    
    # User should be created in database
    from src.ph_stats.domain.entities.user import User
    from sqlmodel import select
    
    result = await db_session.exec(select(User).where(User.email == "<EMAIL>"))
    user = result.first()
    assert user is not None
    assert user.username == "integrationuser"
    assert user.is_active is True

@pytest.mark.asyncio
async def test_user_registration_api_duplicate_email(client: AsyncClient):
    """Test API prevents duplicate email registration"""
    user_data = {
        "email": "<EMAIL>",
        "username": "firstuser",
        "password": "password123"
    }
    
    # Create first user
    response1 = await client.post("/api/v1/auth/register", json=user_data)
    assert response1.status_code == 201
    
    # Attempt to create user with same email
    user_data["username"] = "seconduser"
    response2 = await client.post("/api/v1/auth/register", json=user_data)
    
    assert response2.status_code == 400
    data = response2.json()
    assert "email already registered" in data["detail"].lower()

@pytest.mark.asyncio
async def test_user_registration_api_validation_errors(client: AsyncClient):
    """Test API validation for invalid registration data"""
    invalid_data = {
        "email": "invalid-email",
        "username": "ab",  # Too short
        "password": "123"  # Too short
    }
    
    response = await client.post("/api/v1/auth/register", json=invalid_data)
    
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data
    
    errors = data["detail"]
    # Should have validation errors for email, username, and password
    error_fields = [error["loc"][-1] for error in errors]
    assert "email" in error_fields
    assert "username" in error_fields
    assert "password" in error_fields
```

**🟢 GREEN Phase** - Implement API endpoint:
```python
# src/ph_stats/api/v1/endpoints/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.ph_stats.infrastructure.repositories.session import get_session
from src.ph_stats.application.services.auth_service import AuthService
from src.ph_stats.domain.entities.user import UserCreate, UserResponse

router = APIRouter()

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    session: AsyncSession = Depends(get_session),
    auth_service: AuthService = Depends()
) -> UserResponse:
    """Register new user with email verification."""
    try:
        user = await auth_service.register_user(session, user_data)
        access_token = auth_service.create_access_token({"sub": str(user.id)})
        
        return UserResponse(
            user=user,
            access_token=access_token,
            token_type="bearer",
            expires_in=1800  # 30 minutes
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### Phase 3: Component-Level TDD (Internal Logic)

Write unit tests for business logic and internal components.

**🔴 RED Phase** - Define component behavior with failing unit test:
```python
# tests/unit/services/test_auth_service.py
import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from src.ph_stats.application.services.auth_service import AuthService
from src.ph_stats.domain.entities.user import UserCreate, User

@pytest.mark.asyncio
async def test_auth_service_register_user_success():
    """
    Unit: AuthService.register_user creates user with hashed password
    
    This test defines the service behavior and will fail initially
    as AuthService doesn't exist.
    """
    # Arrange
    auth_service = AuthService()
    mock_session = AsyncMock(spec=AsyncSession)
    
    user_data = UserCreate(
        email="<EMAIL>",
        username="unituser",
        full_name="Unit User",
        password="securepassword123"
    )
    
    # Mock: no existing user found
    mock_session.exec.return_value.first.return_value = None
    
    # Act
    result = await auth_service.register_user(mock_session, user_data)
    
    # Assert
    assert isinstance(result, User)
    assert result.email == "<EMAIL>"
    assert result.username == "unituser"
    assert result.full_name == "Unit User"
    assert result.hashed_password != "securepassword123"  # Password should be hashed
    assert result.is_active is True
    
    # Verify database operations were called
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

def test_auth_service_password_hashing():
    """Test password hashing and verification"""
    auth_service = AuthService()
    password = "testpassword123"
    
    # Hash password
    hashed = auth_service.hash_password(password)
    
    # Verify hashing worked
    assert hashed != password
    assert len(hashed) > 50  # bcrypt hashes are long
    
    # Verify password verification works
    assert auth_service.verify_password(password, hashed)
    assert not auth_service.verify_password("wrongpassword", hashed)

@pytest.mark.asyncio
async def test_auth_service_register_user_duplicate_email():
    """Test service prevents duplicate email registration"""
    auth_service = AuthService()
    mock_session = AsyncMock(spec=AsyncSession)
    
    user_data = UserCreate(
        email="<EMAIL>",
        username="newuser",
        password="password123"
    )
    
    # Mock: existing user found
    existing_user = User(
        id="existing-id",
        email="<EMAIL>",
        username="existinguser",
        hashed_password="hashed"
    )
    mock_session.exec.return_value.first.return_value = existing_user
    
    # Should raise ValueError
    with pytest.raises(ValueError, match="Email already registered"):
        await auth_service.register_user(mock_session, user_data)
    
    # Should not attempt to create user
    mock_session.add.assert_not_called()
    mock_session.commit.assert_not_called()
```

**🟢 GREEN Phase** - Implement service class:
```python
# src/ph_stats/services/auth_service.py
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from src.ph_stats.domain.entities.user import User, UserCreate

class AuthService:
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    async def register_user(self, session: AsyncSession, user_data: UserCreate) -> User:
        """Register new user with hashed password."""
        # Check if user already exists
        result = await session.exec(select(User).where(User.email == user_data.email))
        if result.first():
            raise ValueError("Email already registered")
        
        # Hash password
        hashed_password = self.hash_password(user_data.password)
        
        # Create user
        db_user = User(
            **user_data.model_dump(exclude={"password"}),
            hashed_password=hashed_password
        )
        
        session.add(db_user)
        await session.commit()
        await session.refresh(db_user)
        
        return db_user
```

## TDD Tools and Scripts

### TDD Helper Script

Create `scripts/tdd.sh` for workflow automation:
```bash
#!/bin/bash

# TDD Workflow Helper for Ph Stats FastAPI
# Usage: ./scripts/tdd.sh {red|green|refactor|watch} [test_file]

set -e

case "$1" in
    "red")
        echo "🔴 RED: Running failing test..."
        if [ -z "$2" ]; then
            echo "Usage: $0 red <test_file::test_function>"
            exit 1
        fi
        uv run pytest "$2" -v --tb=short --no-header
        ;;
    "green")
        echo "🟢 GREEN: Running test until it passes..."
        if [ -z "$2" ]; then
            echo "Usage: $0 green <test_file::test_function>"
            exit 1
        fi
        
        echo "Press Ctrl+C when test passes and you want to move to refactor phase"
        while true; do
            echo ""
            echo "Running test: $2"
            if uv run pytest "$2" -v --tb=short --no-header; then
                echo "✅ Test is now passing!"
                break
            else
                echo "❌ Test still failing. Continue implementing..."
                read -p "Press Enter to run test again, or Ctrl+C to exit..."
            fi
        done
        ;;
    "refactor")
        echo "🔵 REFACTOR: Running full test suite to ensure no regressions..."
        uv run pytest tests/unit/ tests/integration/ -v --tb=short
        
        echo ""
        echo "🔧 Running code quality checks..."
        uv run ruff format .
        uv run ruff check .
        uv run mypy src/ tests/
        
        echo "✅ Refactor phase complete!"
        ;;
    "watch")
        echo "👀 WATCH: Running tests on file changes..."
        if [ -z "$2" ]; then
            WATCH_PATH="tests/"
        else
            WATCH_PATH="$2"
        fi
        
        uv run pytest-watch "$WATCH_PATH" -- -v --tb=short
        ;;
    "cycle")
        echo "🔄 FULL TDD CYCLE: Running complete Red-Green-Refactor cycle..."
        if [ -z "$2" ]; then
            echo "Usage: $0 cycle <test_file::test_function>"
            exit 1
        fi
        
        echo "🔴 RED Phase..."
        $0 red "$2"
        
        echo ""
        read -p "Implement minimal code to pass the test, then press Enter to continue..."
        
        echo "🟢 GREEN Phase..."
        $0 green "$2"
        
        echo ""
        read -p "Ready for refactor phase? Press Enter to continue..."
        
        echo "🔵 REFACTOR Phase..."
        $0 refactor
        
        echo "🎉 TDD Cycle complete!"
        ;;
    *)
        echo "TDD Workflow Helper for Ph Stats FastAPI"
        echo ""
        echo "Usage: $0 {red|green|refactor|watch|cycle} [test_file]"
        echo ""
        echo "Commands:"
        echo "  red <test>     - Run a specific failing test (Red phase)"
        echo "  green <test>   - Keep running test until it passes (Green phase)"
        echo "  refactor       - Run full test suite and quality checks (Refactor phase)"
        echo "  watch [path]   - Run tests automatically on file changes"
        echo "  cycle <test>   - Run complete TDD cycle interactively"
        echo ""
        echo "Examples:"
        echo "  $0 red tests/unit/test_user.py::test_user_creation"
        echo "  $0 green tests/unit/test_user.py::test_user_creation"
        echo "  $0 refactor"
        echo "  $0 watch tests/unit/"
        echo "  $0 cycle tests/unit/test_user.py::test_user_creation"
        exit 1
        ;;
esac
```

### Test Execution Patterns

**Single Test Execution**:
```bash
# Run specific test
uv run pytest tests/unit/test_user.py::test_user_creation -v

# Run with debugging
uv run pytest tests/unit/test_user.py::test_user_creation -v -s --pdb

# Run with coverage
uv run pytest tests/unit/test_user.py::test_user_creation --cov=src/ph_stats
```

**Test Categories**:
```bash
# Unit tests (fast)
uv run pytest tests/unit/ -v

# Integration tests (medium speed)
uv run pytest tests/integration/ -v

# E2E tests (slow)
uv run pytest tests/e2e/ -v

# All tests with coverage
uv run pytest --cov=src/ph_stats --cov-report=html
```

**Parallel Testing**:
```bash
# Run tests in parallel
uv run pytest -n auto

# Run specific category in parallel
uv run pytest tests/unit/ -n 4
```

## TDD Best Practices

### Test Writing Guidelines

**1. Test Naming Convention**:
```python
def test_[component]_[scenario]_[expected_result]():
    """Clear description of what the test validates"""
    pass

# Examples:
def test_user_registration_with_valid_data_creates_user():
def test_user_registration_with_duplicate_email_raises_error():
def test_auth_service_password_hashing_produces_different_hash():
```

**2. Test Structure (Arrange-Act-Assert)**:
```python
def test_user_service_create_user():
    # Arrange - Set up test data and mocks
    user_data = UserCreate(email="<EMAIL>", username="test")
    mock_session = AsyncMock()
    
    # Act - Execute the behavior being tested
    result = await user_service.create_user(mock_session, user_data)
    
    # Assert - Verify the expected outcome
    assert result.email == "<EMAIL>"
    mock_session.commit.assert_called_once()
```

**3. Test Data Management**:
```python
# Use factories for consistent test data
user = UserFactory.build(email="<EMAIL>")

# Use builders for complex scenarios
user = UserBuilder().with_email("<EMAIL>").inactive().build()

# Use fixtures for shared setup
@pytest.fixture
def sample_user():
    return UserFactory.build()
```

### Quality Metrics

**Code Coverage Targets**:
- Unit Tests: 95%+ coverage
- Integration Tests: 85%+ API coverage
- E2E Tests: 100% user journey coverage

**Performance Targets**:
- Unit Tests: <10ms average execution
- Integration Tests: <100ms average execution
- E2E Tests: <5s average execution
- Full Test Suite: <60s total execution

### Continuous Integration

Tests run automatically on:
- Pre-commit hooks (unit tests only)
- Pull request creation (full test suite)
- Main branch commits (full suite + E2E)
- Nightly builds (full suite + performance tests)

## Common TDD Patterns

### Testing Async Code
```python
@pytest.mark.asyncio
async def test_async_operation():
    result = await async_function()
    assert result is not None
```

### Testing Exceptions
```python
def test_service_raises_exception_for_invalid_data():
    with pytest.raises(ValueError, match="Invalid data"):
        service.process_invalid_data()
```

### Testing Database Operations
```python
@pytest.mark.asyncio
async def test_database_operation(db_session):
    # Create test data
    user = User(email="<EMAIL>")
    db_session.add(user)
    await db_session.commit()
    
    # Test operation
    result = await service.get_user_by_email(db_session, "<EMAIL>")
    assert result.email == "<EMAIL>"
```

### Testing HTMX Interactions
```python
@pytest.mark.asyncio
async def test_htmx_form_submission(page):
    await page.goto("/form")
    await page.fill('input[name="field"]', "value")
    await page.click('button[type="submit"]')
    
    # Verify HTMX response
    await expect(page.locator('.success')).to_contain_text("Success")
```

## Troubleshooting TDD Issues

### Common Problems and Solutions

**Slow Tests**:
- Use mocks for external dependencies
- Implement proper test database cleanup
- Use parallel test execution
- Profile test execution times

**Flaky Tests**:
- Add proper wait conditions for async operations
- Use deterministic test data
- Implement proper cleanup between tests
- Avoid time-dependent assertions

**Complex Test Setup**:
- Use factory pattern for test data creation
- Create reusable fixtures for common scenarios
- Implement test utilities for complex operations
- Use parametrized tests for multiple scenarios

---

*This TDD workflow guide provides the foundation for test-driven development in Ph Stats FastAPI. For specific testing patterns and examples, refer to the test files in the project repository.*