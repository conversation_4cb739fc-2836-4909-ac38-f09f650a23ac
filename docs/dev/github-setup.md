# GitHub Repository Setup Guide

This guide will help you set up your Ph Stats FastAPI project on GitHub with all the necessary configurations for a professional development workflow.

## 🚀 Quick Setup (5 minutes)

### 1. Create GitHub Repository

1. **Go to GitHub**: Visit [github.com](https://github.com) and sign in
2. **Create New Repository**: Click the "+" icon → "New repository"
3. **Repository Settings**:
   - Repository name: `ph-stats-fastapi`
   - Description: "Modern statistics tracking platform built with FastAPI, SQLModel, HTMX, and Supabase"
   - Visibility: Choose Public or Private
   - **DO NOT** initialize with README, .gitignore, or license (we have these already)

### 2. Connect Local Repository

```bash
# Add GitHub as remote origin
git remote add origin https://github.com/yourusername/ph-stats-fastapi.git

# Push existing code to GitHub
git branch -M main
git push -u origin main

# Push develop branch
git push -u origin develop
```

### 3. Configure Repository Settings

1. **Go to Repository Settings** → Settings tab
2. **Set Default Branch** to `develop`:
   - Go to "Branches" in left sidebar
   - Change default branch from `main` to `develop`
   - Click "Update"

## 🛡️ Branch Protection Setup

### Protect Main Branch

1. **Go to Branches Settings**: Settings → Branches
2. **Add Branch Protection Rule**:
   - Branch name pattern: `main`
   - ✅ Require a pull request before merging
     - ✅ Require approvals: 1
     - ✅ Dismiss stale reviews when new commits are pushed
   - ✅ Require status checks to pass before merging
     - ✅ Require branches to be up to date before merging
     - Add status checks: `lint-and-format`, `test`, `security`
   - ✅ Require linear history
   - ✅ Include administrators
   - ✅ Allow force pushes: ❌
   - ✅ Allow deletions: ❌

### Protect Develop Branch

1. **Add Another Branch Protection Rule**:
   - Branch name pattern: `develop`
   - ✅ Require a pull request before merging
     - ✅ Require approvals: 1
   - ✅ Require status checks to pass before merging
     - Add status checks: `lint-and-format`, `test`, `security`
   - ✅ Include administrators
   - ✅ Allow force pushes: ❌

## ⚙️ Repository Configuration

### Enable Features

1. **Go to Settings → General**
2. **Features Section**:
   - ✅ Issues
   - ✅ Projects
   - ✅ Wiki (optional)
   - ✅ Discussions (recommended)
   - ✅ Sponsorships (optional)

### Set Up Repository Topics

1. **Go to Repository Main Page**
2. **Click Settings Gear** next to "About"
3. **Add Topics**: `fastapi`, `python`, `htmx`, `supabase`, `tailwindcss`, `statistics`, `dashboard`, `tdd`, `async`

### Configure Security

1. **Go to Settings → Security & Analysis**
2. **Enable All Security Features**:
   - ✅ Dependency graph
   - ✅ Dependabot alerts
   - ✅ Dependabot security updates
   - ✅ Secret scanning
   - ✅ Secret scanning push protection

## 🔐 Secrets and Environment Variables

### Required Secrets for CI/CD

1. **Go to Settings → Secrets and Variables → Actions**
2. **Add Repository Secrets**:

```bash
# Database secrets (for testing)
TEST_DATABASE_URL: "postgresql+asyncpg://postgres:testpassword@localhost:5432/testdb"

# Application secrets
SECRET_KEY: "your-super-secret-key-for-testing"

# Supabase secrets (use test project)
SUPABASE_URL: "https://your-test-project.supabase.co"
SUPABASE_ANON_KEY: "your-test-anon-key"
SUPABASE_SERVICE_ROLE_KEY: "your-test-service-role-key"

# Optional: Codecov token for coverage reports
CODECOV_TOKEN: "your-codecov-token"
```

### Environment Variables (if needed)

1. **Go to Settings → Secrets and Variables → Actions**
2. **Variables Tab**:

```bash
PYTHON_VERSION: "3.12"
NODE_VERSION: "20"
ENVIRONMENT: "ci"
```

## 📋 Labels Setup

### Create Issue Labels

Go to Issues → Labels and create these labels:

**Priority Labels:**
- `priority: critical` - #d73a49
- `priority: high` - #ff6b6b  
- `priority: medium` - #fbca04
- `priority: low` - #0052cc

**Type Labels:**
- `bug` - #d73a49
- `feature` - #84b6eb
- `enhancement` - #a2eeef
- `documentation` - #0075ca
- `question` - #cc317c
- `help wanted` - #008672
- `good first issue` - #7057ff

**Status Labels:**
- `status: review needed` - #fbca04
- `status: in progress` - #0052cc
- `status: blocked` - #d73a49
- `status: ready to merge` - #0e8a16

**Area Labels:**
- `area: api` - #c5def5
- `area: frontend` - #bfd4f2
- `area: database` - #d4c5f9
- `area: auth` - #f9c5d4
- `area: testing` - #c5f5d5
- `area: ci/cd` - #f5e5c5

## 🔄 GitHub Actions Configuration

The repository already includes comprehensive GitHub Actions workflows:

### Workflows Overview

1. **`ci.yml`** - Main CI/CD pipeline
   - Linting and formatting checks
   - Unit and integration tests
   - Security scanning
   - Build artifacts

2. **`e2e-tests.yml`** - End-to-end testing
   - Playwright browser tests
   - Cross-browser compatibility
   - UI/UX validation

### Verify Actions Work

1. **Make a test commit**:
   ```bash
   echo "# Test" >> test.md
   git add test.md
   git commit -m "test: verify GitHub Actions"
   git push origin develop
   ```

2. **Check Actions Tab**: Go to Actions tab and verify workflows run successfully

3. **Clean up**:
   ```bash
   git rm test.md
   git commit -m "chore: remove test file"
   git push origin develop
   ```

## 📊 Project Management Setup

### Enable GitHub Projects (Optional)

1. **Go to Projects Tab**
2. **Create New Project**:
   - Name: "Ph Stats Development"
   - Template: "Feature development"
   - Add columns: Backlog, Sprint, In Progress, Review, Done

### Enable Discussions

1. **Go to Settings → General → Features**
2. **Enable Discussions**
3. **Create Categories**:
   - 💬 General
   - 💡 Ideas
   - 🙋 Q&A
   - 📢 Announcements
   - 🐛 Troubleshooting

## 🔗 Integration Setup

### Code Coverage (Codecov)

1. **Sign up at [codecov.io](https://codecov.io)**
2. **Connect your repository**
3. **Add CODECOV_TOKEN to repository secrets**

### Code Quality (CodeQL)

1. **Go to Settings → Security & Analysis**
2. **Enable CodeQL Analysis**
3. **Set up default configuration**

## 🚀 Deployment Integration

### Render.com Integration

1. **Connect Repository**: In Render.com dashboard, connect your GitHub repository
2. **Auto-Deploy Settings**: 
   - Branch: `main`
   - Build Command: `uv sync --all-extras && uv run python -c "import uvicorn; print('Build successful')"`
   - Start Command: `uv run uvicorn src.ph_stats.main:app --host 0.0.0.0 --port $PORT`

### Environment Variables in Render

Set these in Render.com dashboard:
```bash
PYTHON_VERSION=3.12
ENVIRONMENT=production
SUPABASE_URL=your-production-supabase-url
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
DATABASE_URL=your-supabase-connection-string
SECRET_KEY=your-production-secret-key
```

## 📖 Documentation

### Update README.md

The repository should have a comprehensive README with:
- Project description
- Setup instructions
- API documentation links
- Contribution guidelines
- License information

### Wiki Setup (Optional)

1. **Enable Wiki** in repository settings
2. **Create Pages**:
   - Home
   - API Documentation
   - Deployment Guide
   - Troubleshooting
   - FAQ

## ✅ Verification Checklist

After completing the setup:

- [ ] Repository created and connected
- [ ] Branch protection rules configured
- [ ] GitHub Actions workflows running
- [ ] Issue and PR templates working
- [ ] Labels created and organized
- [ ] Security features enabled
- [ ] Secrets and variables configured
- [ ] Integration services connected
- [ ] Documentation updated
- [ ] Team members added (if applicable)

## 🔧 Advanced Configuration

### Custom Domain (if applicable)

1. **Buy domain**: e.g., `ph-stats.com`
2. **Configure DNS**: Point to Render.com
3. **Update Render settings**: Add custom domain
4. **Update Supabase**: Add domain to allowed origins

### Monitoring Integration

1. **Sentry**: Error tracking and performance monitoring
2. **LogRocket**: User session recordings
3. **Mixpanel**: User analytics and tracking

### Team Collaboration

1. **Add Collaborators**: Settings → Manage Access
2. **Create Teams**: Organization → Teams (if using organization)
3. **Set Permissions**: Read, Write, Admin levels

## 🆘 Troubleshooting

### Common Issues

**GitHub Actions Failing:**
- Check secrets are properly set
- Verify workflow YAML syntax
- Check branch protection rules

**Branch Protection Issues:**
- Ensure status checks exist before adding them
- Admin override might be needed initially

**Integration Problems:**
- Verify webhook URLs
- Check API keys and permissions
- Review service configurations

### Getting Help

- **GitHub Documentation**: [docs.github.com](https://docs.github.com)
- **Community Forum**: [github.community](https://github.community)
- **Support**: Contact GitHub support for technical issues

---

*This setup provides a professional, secure, and efficient development workflow for your Ph Stats FastAPI project. The configuration ensures code quality, security, and smooth collaboration.*