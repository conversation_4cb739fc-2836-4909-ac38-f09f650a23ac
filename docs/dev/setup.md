# Development Environment Setup

This guide will help you set up a complete development environment for Ph Stats FastAPI from scratch.

## Prerequisites

### Required Software
- **Python 3.12+**: Modern Python with latest async features
- **uv**: Modern Python package manager ([installation guide](https://docs.astral.sh/uv/getting-started/installation/))
- **Node.js 18+**: For Tailwind CSS build process ([installation guide](https://nodejs.org/))
- **Git**: Version control system
- **Supabase Account**: For managed database and authentication ([signup here](https://supabase.com))

### Optional Local Development
- **PostgreSQL**: Optional local database for offline development
- **Redis**: Optional local caching server for development

### System Requirements
- **OS**: macOS, Linux, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: At least 5GB free space
- **Network**: Internet connection for package downloads

## Quick Start

### 1. Project Initialization

```bash
# Create new project directory
mkdir ph-stats-fastapi && cd ph-stats-fastapi

# Initialize with uv
uv init --app

# Create project structure
mkdir -p src/ph_stats/{core,database,models,api,services,templates,utils}
mkdir -p tests/{unit,integration,e2e,factories,fixtures}
mkdir -p static/{css,js,images}
mkdir -p alembic/versions
mkdir -p scripts docs .github/workflows
```

### 2. Dependency Management

Create `pyproject.toml` with comprehensive dependencies:

```toml
[project]
name = "ph-stats-fastapi"
version = "0.1.0"
description = "Statistics tracking platform with FastAPI, SQLModel, and HTMX"
requires-python = ">=3.12"

dependencies = [
    # Core Framework
    "fastapi[all]>=0.100.0",
    "uvicorn[standard]>=0.24.0",
    
    # Database & ORM
    "sqlmodel>=0.0.18",
    "asyncpg>=0.29.0",
    "supabase>=2.0.0",
    "postgrest>=0.10.0",
    "alembic>=1.13.0",
    
    # Authentication & Security  
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    
    # Templates & Static Files
    "jinja2>=3.1.0",
    "aiofiles>=23.2.0",
    
    # Configuration & Settings
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    
    # HTTP Client & Utilities
    "httpx>=0.25.0",
    "rich>=13.7.0",
    
    # Email Support
    "fastapi-mail>=1.4.0",
    
    # Caching & Performance
    "redis>=5.0.0",
]

[tool.uv.dev-dependencies]
# Testing Framework
pytest = ">=8.0.0"
pytest-asyncio = ">=0.23.0"
pytest-cov = ">=4.1.0"
pytest-xdist = ">=3.3.0"
pytest-html = ">=4.1.0"
pytest-mock = ">=3.12.0"

# E2E Testing
playwright = ">=1.40.0"
factory-boy = ">=3.3.0"
freezegun = ">=1.2.0"

# Code Quality
ruff = ">=0.12.8"
mypy = ">=1.17.1"
pre-commit = ">=3.6.0"

# Security & Performance Testing
bandit = ">=1.7.0"
locust = ">=2.17.0"

# Development Tools
watchfiles = ">=0.21.0"
ipython = ">=8.18.0"
```

Install dependencies:
```bash
uv sync
```

### Frontend Dependencies (Tailwind CSS)

Create `package.json`:
```json
{
  "name": "ph-stats-fastapi",
  "version": "0.1.0",
  "description": "Frontend dependencies for Ph Stats FastAPI",
  "scripts": {
    "build-css": "tailwindcss -i ./static/src/input.css -o ./static/dist/output.css --watch",
    "build-css-prod": "tailwindcss -i ./static/src/input.css -o ./static/dist/output.css --minify"
  },
  "devDependencies": {
    "tailwindcss": "^3.4.0",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10",
    "@tailwindcss/aspect-ratio": "^0.4.2"
  }
}
```

Install frontend dependencies:
```bash
npm install
```

Create Tailwind configuration (`tailwind.config.js`):
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/ph_stats/templates/**/*.{html,jinja,jinja2}",
    "./src/ph_stats/templates/**/*.py"  // For JinjaX components
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
```

Create input CSS file (`static/src/input.css`):
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }
}
```

### 3. Environment Configuration

Create `.env.example`:
```bash
# Application Settings
APP_NAME="Ph Stats FastAPI"
APP_VERSION="0.1.0"
DEBUG=true
SECRET_KEY="your-super-secret-key-change-in-production"

# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"  # For admin operations

# Optional Database Configuration (for direct SQL access)
DATABASE_URL="postgresql+asyncpg://postgres:[password]@db.[project].supabase.co:5432/postgres"
DATABASE_ECHO=false

# Authentication Settings (managed by Supabase)
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Optional Redis Configuration (for caching)
REDIS_URL="redis://localhost:6379/0"  # Local development
# REDIS_URL="rediss://..." # Render.com Redis addon in production

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"
```

Copy to actual environment file:
```bash
cp .env.example .env
# Edit .env with your actual values
```

### 4. Supabase Project Setup

Create and configure your Supabase project:
```bash
# 1. Go to https://supabase.com/dashboard
# 2. Create a new project
# 3. Wait for the project to be ready (usually 2-3 minutes)
# 4. Go to Settings > API to get your project URL and keys

# 5. Update your .env file with your Supabase credentials
cp .env.example .env
# Edit .env file with your actual Supabase project details
```

Optional: Install Supabase CLI for local development:
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Initialize local development (optional)
supabase init

# Start local Supabase stack (optional - for offline development)
supabase start
```

Optional: Install local services for caching:
```bash
# Redis for local caching (optional)
# macOS with Homebrew
brew install redis
brew services start redis

# Ubuntu/Debian  
sudo apt install redis-server
sudo systemctl start redis-server

# Verify Redis is running
redis-cli ping  # Should return "PONG"
```

### 5. Database Schema Setup

Option 1: Use Supabase Dashboard (Recommended):
```bash
# 1. Go to your Supabase project dashboard
# 2. Navigate to Table Editor
# 3. Create tables using the visual editor
# 4. Set up Row Level Security (RLS) policies
# 5. Configure authentication settings
```

Option 2: Use SQL migrations with Supabase:
```bash  
# 1. Go to SQL Editor in Supabase dashboard
# 2. Create and run SQL migration scripts
# 3. Alternatively, use Supabase CLI for migration management

# Example: Create users table with RLS
supabase migration new create_users_table
# Edit the generated migration file
supabase db push
```

Option 3: Traditional Alembic (if needed):
```bash
# Create alembic.ini for direct database connection
# Only use if you need custom migration logic

uv run alembic init alembic
# Edit alembic.ini to point to your Supabase database
# sqlalchemy.url = postgresql+asyncpg://postgres:[password]@db.[project].supabase.co:5432/postgres
```

### 6. Testing Setup

Create `tests/conftest.py`:
```python
import asyncio
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel

from src.ph_stats.main import create_app
from src.ph_stats.infrastructure.repositories.session import get_session
from src.ph_stats.config import Settings

# Test configuration
@pytest.fixture(scope="session") 
def test_settings():
    return Settings(
        SUPABASE_URL="https://your-test-project.supabase.co",
        SUPABASE_ANON_KEY="test-anon-key",
        SUPABASE_SERVICE_ROLE_KEY="test-service-role-key",
        SECRET_KEY="test-secret-key",
        DEBUG=True
    )

# Database fixtures
@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest_asyncio.fixture(scope="session")
async def test_engine(test_settings):
    engine = create_async_engine(
        test_settings.DATABASE_URL,
        echo=False,
        future=True
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
    
    yield engine
    
    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.drop_all)
    await engine.dispose()

@pytest_asyncio.fixture
async def db_session(test_engine) -> AsyncSession:
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()

@pytest_asyncio.fixture
async def client(db_session, test_settings) -> AsyncClient:
    app = create_app()
    
    async def override_get_session():
        yield db_session
    
    app.dependency_overrides[get_session] = override_get_session
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()
```

Install Playwright:
```bash
uv run playwright install --with-deps chromium
```

### 7. Code Quality Setup

Create `.pre-commit-config.yaml`:
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.6
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

Install pre-commit hooks:
```bash
uv run pre-commit install
```

### 8. Development Scripts

Create `scripts/setup.sh`:
```bash
#!/bin/bash
set -e

echo "🚀 Setting up Ph Stats FastAPI development environment..."

# Check prerequisites
command -v uv >/dev/null 2>&1 || { echo "❌ uv is required but not installed. Aborting." >&2; exit 1; }
command -v psql >/dev/null 2>&1 || { echo "❌ PostgreSQL is required but not installed. Aborting." >&2; exit 1; }
command -v redis-cli >/dev/null 2>&1 || { echo "❌ Redis is required but not installed. Aborting." >&2; exit 1; }

# Verify services are running
echo "🔍 Checking services..."
redis-cli ping >/dev/null 2>&1 || { echo "❌ Redis is not running. Start with: brew services start redis (macOS) or sudo systemctl start redis-server (Linux)" >&2; exit 1; }
pg_isready >/dev/null 2>&1 || { echo "❌ PostgreSQL is not running. Start with: brew services start postgresql@15 (macOS) or sudo systemctl start postgresql (Linux)" >&2; exit 1; }

# Install Python dependencies
echo "📦 Installing dependencies..."
uv sync

# Initialize database
echo "🗄️ Initializing database..."
uv run alembic upgrade head

# Install pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
uv run pre-commit install

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
uv run playwright install --with-deps chromium

# Run initial tests
echo "🧪 Running initial tests..."
uv run pytest tests/ -v

echo "✅ Development environment setup complete!"
echo "🔗 Start the development server with: uv run uvicorn src.ph_stats.main:app --reload"
echo ""
echo "💡 Troubleshooting:"
echo "   - PostgreSQL: Check with 'pg_isready' and ensure databases exist"
echo "   - Redis: Check with 'redis-cli ping'"
echo "   - Environment: Verify .env file has correct database credentials"
```

Make executable and run:
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

## Development Workflow

### 1. Daily Development

Start development servers:
```bash
# Terminal 1: Start FastAPI development server
uv run uvicorn src.ph_stats.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Start Tailwind CSS watcher (builds CSS automatically)
npm run build-css
```

Run tests:
```bash
# All tests
uv run pytest

# Unit tests only
uv run pytest tests/unit/ -v

# With coverage
uv run pytest --cov=src/ph_stats --cov-report=html
```

### 2. TDD Workflow

Use the TDD helper script (`scripts/tdd.sh`):
```bash
# Red: Write failing test
./scripts/tdd.sh red tests/unit/test_new_feature.py::test_specific_behavior

# Green: Make test pass
./scripts/tdd.sh green tests/unit/test_new_feature.py::test_specific_behavior

# Refactor: Improve code quality
./scripts/tdd.sh refactor

# Watch mode for continuous testing
./scripts/tdd.sh watch tests/unit/
```

### 3. Code Quality Checks

Manual quality checks:
```bash
# Format code
uv run ruff format .

# Lint code
uv run ruff check .

# Type checking
uv run mypy src/ tests/

# Security scans
uv run python -m safety scan --stage development --disable-optional-telemetry
uv run python -m bandit -r src/
```

Automated checks (runs on commit):
```bash
uv run pre-commit run --all-files
```

### 4. Database Management

Create migration:
```bash
uv run alembic revision --autogenerate -m "Add user table"
```

Apply migrations:
```bash
uv run alembic upgrade head
```

Reset database:
```bash
uv run alembic downgrade base
uv run alembic upgrade head
```

## IDE Configuration

### VS Code Setup

Recommended extensions (`.vscode/extensions.json`):
```json
{
    "recommendations": [
        "ms-python.python",
        "ms-python.mypy-type-checker",
        "charliermarsh.ruff",
        "ms-python.pylint",
        "ms-playwright.playwright",
        "bradlc.vscode-tailwindcss",
        "redhat.vscode-yaml"
    ]
}
```

Settings (`.vscode/settings.json`):
```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.linting.enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "none",
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll.ruff": true
        }
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true
    }
}
```

## Troubleshooting

### Common Issues

**Database Connection Issues**:
```bash
# Check if PostgreSQL is running
pg_isready

# Check PostgreSQL status (Linux)
sudo systemctl status postgresql

# Check PostgreSQL status (macOS)
brew services list | grep postgresql

# Restart PostgreSQL if needed
sudo systemctl restart postgresql  # Linux
brew services restart postgresql@15  # macOS
```

**Python Environment Issues**:
```bash
# Recreate virtual environment
uv venv --force
uv sync
```

**Port Conflicts**:
```bash
# Check what's using port 8000
lsof -i :8000

# Use different port
uv run uvicorn src.ph_stats.main:app --reload --port 8001
```

**Test Database Issues**:
```bash
# Recreate test database
dropdb test_ph_stats
createdb test_ph_stats
uv run alembic -x database_url=postgresql+asyncpg://$(whoami):development@localhost:5432/test_ph_stats upgrade head
```

**Redis Connection Issues**:
```bash
# Check if Redis is running
redis-cli ping

# Check Redis status (Linux) 
sudo systemctl status redis-server

# Check Redis status (macOS)
brew services list | grep redis

# Restart Redis if needed
sudo systemctl restart redis-server  # Linux
brew services restart redis  # macOS
```

### Performance Issues

Monitor development server performance:
```bash
# Install development monitoring
uv add --dev py-spy

# Profile running application
uv run py-spy record -o profile.svg -- python -m uvicorn src.ph_stats.main:app
```

## Next Steps

After completing the setup:

1. **Read the Architecture**: Review [Architecture Documentation](../architecture.md)
2. **Understand TDD Workflow**: Study [TDD Workflow Guide](tdd-workflow.md)
3. **Follow Implementation Plan**: Check [Implementation Roadmap](implementation-roadmap.md)
4. **Start Development**: Begin with the first TDD cycle

---

*This setup guide provides everything needed to start developing Ph Stats FastAPI. For ongoing development practices, refer to the [Development Guidelines](guidelines.md).*