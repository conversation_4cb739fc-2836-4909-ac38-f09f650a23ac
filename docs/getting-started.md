# Getting Started

Welcome to Ph Stats FastAPI! This guide will help you get the application up and running quickly, whether you're a developer looking to contribute or someone interested in understanding the system.

## Quick Start (5 Minutes)

### Prerequisites
- **Python 3.12+** installed on your system
- **Supabase account** for database and authentication ([signup here](https://supabase.com))
- **uv** package manager ([install here](https://docs.astral.sh/uv/getting-started/installation/))
- **Git** for version control
- **Render.com account** for deployment (optional, [signup here](https://render.com))

### 1. Clone and Setup
```bash
# Clone the repository (replace with actual repo URL)
git clone https://github.com/yourusername/ph-stats-fastapi.git
cd ph-stats-fastapi

# Run the automated setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Setup Supabase Project
```bash
# Create a new Supabase project at https://supabase.com/dashboard
# Note down your project URL and anon key from the API settings

# Update your .env file with Supabase credentials
cp .env.example .env
# Edit .env with your Supabase project details
```

### 3. Setup Frontend Assets
```bash
# Install Node.js dependencies for Tailwind CSS (if using build process)
npm install -D tailwindcss @tailwindcss/forms @tailwindcss/typography

# Or use Tailwind CSS CDN in templates (for rapid development)
# Add to base template: <script src="https://cdn.tailwindcss.com"></script>

# Access Tailwind Plus UI Blocks at https://tailwindplus.com
# Copy components and adapt for JinjaX templates
```

### 4. Start the Application
```bash
# Start the development server
uv run uvicorn src.ph_stats.main:app --reload
```

### 5. Access the Application
- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc
- **Supabase Dashboard**: https://supabase.com/dashboard (for database management)
- **Tailwind Plus UI Blocks**: https://tailwindplus.com (for component reference)

That's it! You now have Ph Stats FastAPI running locally.

## What You'll See

### Dashboard Interface
The main dashboard provides:
- **Statistics Overview**: Clean, card-based layout using Tailwind Plus UI blocks
- **Real-time Updates**: Live data refreshing via HTMX with smooth transitions
- **Interactive Charts**: Visualizations with Tailwind-styled containers
- **Responsive Design**: Mobile-first design using Tailwind's responsive utilities
- **Modern UI**: Beautiful interfaces built with Tailwind CSS utility classes

### API Interface
The automatically generated API documentation includes:
- **Interactive Testing**: Try API endpoints directly in the browser
- **Request/Response Examples**: See exactly how to use each endpoint
- **Authentication Testing**: Test secured endpoints with JWT tokens

## Core Concepts

### Users & Authentication
- **Registration**: Create an account with email verification
- **JWT Authentication**: Secure, stateless authentication
- **Profile Management**: Update your profile and preferences

### Statistics Collection
- **Flexible Metrics**: Record any numerical data with custom labels
- **Tags & Categorization**: Organize your metrics with tags
- **Time Series Data**: Automatic timestamp tracking for trend analysis

### Real-time Updates
- **HTMX Integration**: Dynamic UI updates without complex JavaScript
- **Live Dashboards**: Statistics update automatically as data changes
- **Progressive Enhancement**: Works with or without JavaScript enabled

## Basic Usage Examples

### 1. Register a New User
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "newuser",
    "full_name": "New User",
    "password": "securepassword123"
  }'
```

### 2. Login and Get Token
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### 3. Record a Statistic
```bash
# Use the token from login response
curl -X POST "http://localhost:8000/api/v1/stats" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "metric_name": "page_views",
    "metric_value": 1250,
    "metric_unit": "views",
    "tags": {"page": "homepage", "category": "engagement"}
  }'
```

### 4. Get Statistics Summary
```bash
curl -X GET "http://localhost:8000/api/v1/analytics/summary" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Architecture Overview

Ph Stats FastAPI is built with modern technologies for performance and maintainability:

### Backend Stack
- **FastAPI**: High-performance async web framework
- **SQLModel**: Type-safe ORM combining Pydantic and SQLAlchemy
- **Supabase**: Managed PostgreSQL with built-in authentication and real-time features
- **Render.com**: Cloud deployment platform with automatic scaling

### Frontend Stack
- **HTMX**: Dynamic interactions without complex JavaScript
- **Jinja2 + JinjaX**: Server-side template rendering with component composition
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **Tailwind Plus UI Blocks**: Pre-built components and design patterns
- **Chart.js**: Interactive data visualizations

### Development Stack
- **pytest**: Comprehensive testing framework
- **Playwright**: End-to-end browser testing
- **Ruff**: Fast Python linting and formatting
- **uv**: Modern Python package management

## Project Structure

```
ph-stats-fastapi/
├── src/ph_stats/          # Main application code
│   ├── api/              # API endpoints and routes
│   ├── models/           # Database models and schemas
│   ├── services/         # Business logic layer
│   ├── templates/        # HTMX templates
│   └── core/             # Core functionality
├── tests/                # Comprehensive test suite
│   ├── unit/            # Fast, isolated unit tests
│   ├── integration/     # API and database tests
│   └── e2e/             # End-to-end browser tests
├── static/              # CSS, JavaScript, images
├── docs/                # Project documentation
└── scripts/             # Development and deployment scripts
```

## Next Steps

### For Developers
1. **Read the Architecture**: [System Architecture](architecture.md)
2. **Setup Development Environment**: [Development Setup](dev/setup.md)
3. **Understand TDD Workflow**: [TDD Guide](dev/tdd-workflow.md)
4. **Follow Implementation Plan**: [Implementation Roadmap](dev/implementation-roadmap.md)

### For API Users
1. **Explore API Documentation**: Visit http://localhost:8000/docs
2. **Try Interactive Examples**: Use the built-in API testing interface
3. **Read API Reference**: [API Documentation](api-reference.md)
4. **Understand Authentication**: [Security Implementation](tech/security.md)

### For Operators
1. **Deployment Guide**: [Production Deployment](deployment.md)
2. **Monitoring Setup**: [Monitoring Guide](ops/monitoring.md)
3. **Troubleshooting**: [Common Issues](ops/troubleshooting.md)

## Common Issues & Solutions

### Port Already in Use
```bash
# Check what's using port 8000
lsof -i :8000

# Use a different port
uv run uvicorn src.ph_stats.main:app --reload --port 8001
```

### Database Connection Issues
```bash
# Check Supabase connection
# Verify your SUPABASE_URL and SUPABASE_ANON_KEY in .env file

# Test connection with curl
curl -X GET 'https://your-project.supabase.co/rest/v1/' \
  -H "apikey: your-anon-key" \
  -H "Authorization: Bearer your-anon-key"

# Check Supabase project status at https://supabase.com/dashboard
```

### Package Installation Problems
```bash
# Recreate virtual environment
uv venv --force
uv sync
```

### Test Failures
```bash
# Run tests with verbose output
uv run pytest -v

# Run specific test category
uv run pytest tests/unit/ -v
```

## Getting Help

### Documentation
- **Complete Documentation**: Browse the [docs/](.) directory
- **API Reference**: Available at `/docs` when running the application
- **Development Guides**: See [dev/](dev/) directory for detailed guides

### Community & Support
- **Issues**: Report bugs and request features on GitHub
- **Discussions**: Join community discussions for questions and ideas
- **Contributing**: See the contribution guidelines for code contributions

### Development Resources
- **Architecture Decisions**: [ADR Documentation](dev/architecture-decisions.md)
- **Testing Strategy**: [Testing Guide](dev/testing-strategy.md)
- **Performance Guidelines**: [Performance Optimization](tech/performance.md)

## Configuration Options

### Environment Variables
The application can be configured via environment variables:

```bash
# Application Settings
APP_NAME="Ph Stats FastAPI"
DEBUG=true
SECRET_KEY="your-secret-key"

# Supabase Configuration
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"  # For admin operations

# Authentication Settings (handled by Supabase)
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Optional Redis Configuration (for caching)
REDIS_URL="redis://localhost:6379/0"  # Local development
# REDIS_URL="rediss://..." # Render.com Redis addon in production
```

### Feature Flags
Enable or disable features via configuration:

```bash
# Feature toggles
ENABLE_EMAIL_VERIFICATION=true
ENABLE_WEBSOCKETS=true
ENABLE_ANALYTICS_EXPORT=true
```

## Performance Considerations

### Development Mode
- Uses Supabase development project for testing
- Hot reload enabled for rapid development
- Comprehensive logging for debugging
- Supabase dashboard for database inspection

### Production Mode
- Supabase production project with connection pooling
- Render.com Redis addon for caching and session storage
- CDN and edge caching via Render.com and Supabase
- Integrated monitoring via Render.com and Supabase dashboards

## Security Notes

### Development Security
- Use Supabase development project with limited access
- Anon keys are safe for client-side use
- RLS policies protect data access
- CORS configured for local development

### Production Security
- Supabase production project with proper RLS policies
- Service role keys kept secure and used only server-side
- Render.com provides SSL/TLS automatically
- Security headers and CORS properly configured
- Supabase handles security updates and patches automatically

---

*This getting started guide provides everything you need to begin using Ph Stats FastAPI. For more detailed information, explore the comprehensive documentation in the [docs/](.) directory.*