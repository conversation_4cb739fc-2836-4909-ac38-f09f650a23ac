"""E2E test configuration and fixtures."""

import subprocess
import time
from collections.abc import Generator

import httpx
import pytest
import requests
from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright, async_playwright

from tests.e2e.constants import HEALTH_CHECK_TIMEOUT, HTTP_OK, SERVER_START_TIMEOUT


@pytest.fixture(scope="function")
async def playwright() -> Playwright:
    """Start Playwright for each test function."""
    async with async_playwright() as p:
        yield p


@pytest.fixture(scope="function")
async def browser(playwright: Playwright) -> Browser:
    """Launch browser for each test."""
    browser = await playwright.chromium.launch(headless=True)
    yield browser
    await browser.close()


@pytest.fixture(scope="session")
def app_server() -> Generator[str, None, None]:
    """Start the FastAPI application server for testing."""
    # Start the server in the background
    process = subprocess.Popen(
        ["uv", "run", "python", "-m", "uvicorn", "src.app:app", "--host", "127.0.0.1", "--port", "8000"],  # noqa: S607
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    # Wait for server to start (synchronous)
    base_url = "http://127.0.0.1:8000"
    max_retries = SERVER_START_TIMEOUT
    retry_count = 0

    while retry_count < max_retries:
        try:
            response = requests.get(f"{base_url}/health", timeout=HEALTH_CHECK_TIMEOUT)
            if response.status_code == HTTP_OK:
                break
        except (requests.exceptions.RequestException, requests.exceptions.Timeout):
            pass

        retry_count += 1
        time.sleep(1)

    if retry_count >= max_retries:
        process.terminate()
        raise RuntimeError("Failed to start test server")

    yield base_url

    # Clean up
    process.terminate()
    process.wait()


@pytest.fixture
async def context(browser: Browser) -> BrowserContext:
    """Create a new browser context for each test."""
    context = await browser.new_context()
    yield context
    await context.close()


@pytest.fixture
async def page(context: BrowserContext) -> Page:
    """Create a new page for each test."""
    page = await context.new_page()
    yield page
    await page.close()


@pytest.fixture
async def http_client(app_server: str) -> httpx.AsyncClient:
    """Create an HTTP client for API testing."""
    async with httpx.AsyncClient(base_url=app_server) as client:
        yield client
