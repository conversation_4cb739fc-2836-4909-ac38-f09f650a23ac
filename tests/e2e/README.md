# E2E Tests

This directory contains end-to-end tests for the Powerhouse Stats FastAPI application.

## Overview

The E2E tests validate the application from a user's perspective, testing both API endpoints and browser interactions.

## Test Structure

```
tests/e2e/
├── conftest.py              # Test configuration and fixtures
├── test_health_check.py     # Health and basic endpoint tests  
├── test_app_functionality.py # Core application functionality tests
└── README.md               # This file
```

## Test Categories

Tests are marked with the following pytest markers:

- `@pytest.mark.e2e` - All end-to-end tests
- `@pytest.mark.api` - Tests that make HTTP API calls
- `@pytest.mark.browser` - Tests that require browser automation
- `@pytest.mark.slow` - Tests that may take longer to execute

## Running Tests

### Prerequisites

1. Install Playwright browsers:
```bash
uv run python -m playwright install chromium
```

### Run All E2E Tests
```bash
uv run python -m pytest tests/e2e/ -v
```

### Run Specific Test Categories
```bash
# Run only API tests (faster)
uv run python -m pytest tests/e2e -m "api" -v

# Run only browser tests  
uv run python -m pytest tests/e2e -m "browser" -v

# Run all E2E tests
uv run python -m pytest tests/e2e -m "e2e" -v
```

### Run Specific Test Files
```bash
# Health check tests
uv run python -m pytest tests/e2e/test_health_check.py -v

# App functionality tests
uv run python -m pytest tests/e2e/test_app_functionality.py -v
```

### Run with Coverage
```bash
uv run python -m pytest tests/e2e/ -v --cov=src --cov-report=html
```

## Test Configuration

E2E tests automatically:
- Start the FastAPI application server on `http://127.0.0.1:8000`
- Wait for the server to become available
- Create browser instances for browser tests
- Clean up resources after tests complete

## Fixtures Available

- `app_server` - URL of the running test server
- `http_client` - Async HTTP client for API calls
- `browser` - Playwright browser instance
- `context` - Browser context (isolated per test)
- `page` - Browser page instance

## Adding New Tests

1. Create new test files following the `test_*.py` pattern
2. Use appropriate markers (`@pytest.mark.e2e`, `@pytest.mark.api`, etc.)
3. Use the provided fixtures for server interaction
4. Follow async/await patterns for all test methods

## CI/CD Integration

E2E tests run automatically in GitHub Actions:
- Tests run against PostgreSQL database
- Playwright browsers are installed automatically
- Tests validate application startup and health endpoints