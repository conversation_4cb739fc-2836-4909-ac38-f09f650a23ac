"""E2E tests for basic application functionality."""

import httpx
import pytest
from playwright.async_api import Page, expect

from tests.e2e.constants import HTTP_OK


@pytest.mark.e2e
class TestAppFunctionality:
    """Test basic application functionality."""

    async def test_app_starts_successfully(self, app_server: str, http_client: httpx.AsyncClient) -> None:
        """Test that the application starts and responds correctly."""
        # Test health endpoint
        health_response = await http_client.get("/health")
        assert health_response.status_code == HTTP_OK

        # Test root endpoint
        root_response = await http_client.get("/")
        assert root_response.status_code == HTTP_OK

    async def test_fastapi_openapi_docs(self, page: Page, app_server: str) -> None:
        """Test that FastAPI auto-generated docs are accessible."""
        # Set page timeout
        page.set_default_timeout(10000)  # 10 seconds

        # Test OpenAPI docs endpoint
        await page.goto(f"{app_server}/docs", wait_until="networkidle", timeout=15000)

        # Check that Swagger UI loads
        await expect(page.locator("text=FastAPI")).to_be_visible(timeout=5000)
        await expect(page.locator("text=Powerhouse Stats FastAPI")).to_be_visible(timeout=5000)

    async def test_openapi_json_endpoint(self, http_client: httpx.AsyncClient) -> None:
        """Test OpenAPI JSON specification endpoint."""
        response = await http_client.get("/openapi.json")

        assert response.status_code == HTTP_OK
        openapi_spec = response.json()

        assert openapi_spec["info"]["title"] == "Powerhouse Stats FastAPI"
        assert openapi_spec["info"]["version"] == "0.1.0"
        assert "/health" in openapi_spec["paths"]
        assert "/" in openapi_spec["paths"]

    async def test_redoc_documentation(self, page: Page, app_server: str) -> None:
        """Test that ReDoc documentation is accessible."""
        await page.goto(f"{app_server}/redoc")

        # Check that ReDoc loads
        await expect(page.locator("text=Powerhouse Stats FastAPI")).to_be_visible()
        await expect(page.locator("h1")).to_be_visible()

    async def test_cors_headers(self, http_client: httpx.AsyncClient) -> None:
        """Test CORS headers are present (if configured)."""
        response = await http_client.get("/health")

        # Basic check - the endpoint should work
        assert response.status_code == HTTP_OK

        # Note: CORS headers would be tested here if configured
        # Example: assert "access-control-allow-origin" in response.headers

    @pytest.mark.parametrize("endpoint", ["/", "/health", "/docs", "/redoc", "/openapi.json"])
    async def test_all_endpoints_respond(self, http_client: httpx.AsyncClient, endpoint: str) -> None:
        """Test that all main endpoints respond with success status codes."""
        response = await http_client.get(endpoint)

        # All these endpoints should return successful responses
        assert response.status_code in [200, 301, 302], f"Endpoint {endpoint} failed with status {response.status_code}"
