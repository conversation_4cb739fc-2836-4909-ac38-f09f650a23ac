"""E2E tests for health check endpoints."""

import httpx
import pytest
from playwright.async_api import Page

from tests.e2e.constants import HTTP_OK


@pytest.mark.e2e
class TestHealthCheck:
    """Test health check functionality."""

    @pytest.mark.api
    async def test_health_endpoint_api(self, http_client: httpx.AsyncClient) -> None:
        """Test health endpoint via API."""
        response = await http_client.get("/health")

        assert response.status_code == HTTP_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "ph-stats-fastapi"

    @pytest.mark.browser
    async def test_health_endpoint_browser(self, page: Page, app_server: str) -> None:
        """Test health endpoint via browser."""
        await page.goto(f"{app_server}/health")

        # Check that the page loads without errors
        content = await page.content()
        assert "healthy" in content
        assert "ph-stats-fastapi" in content

    @pytest.mark.api
    async def test_root_endpoint_api(self, http_client: httpx.AsyncClient) -> None:
        """Test root endpoint via API."""
        response = await http_client.get("/")

        assert response.status_code == HTTP_OK
        data = response.json()
        assert data["greeting"] == "Hello"
        assert data["name"] == "World"

    @pytest.mark.browser
    async def test_root_endpoint_browser(self, page: Page, app_server: str) -> None:
        """Test root endpoint via browser."""
        await page.goto(f"{app_server}/")

        # Check that the page loads without errors
        content = await page.content()
        assert "Hello" in content
        assert "World" in content
