# Claude Code Commands

This directory contains custom Claude Code commands for the ph-stats-fastapi project.

## Available Commands

### `/plan` - Feature Planning and Task Breakdown

The `/plan` command creates detailed, actionable task breakdowns following TDD methodology and clean architecture principles.

#### Usage Examples

```bash
# Plan a new feature
/plan user-authentication --type feature --scope medium

# Plan a refactoring task with interactive questioning
/plan optimize-database-queries --type refactor --scope small --interactive

# Plan infrastructure work
/plan redis-caching --type infrastructure --scope large

# Dry run to see planning output without creating files
/plan api-validation --dry-run
```

#### Output

The command generates a comprehensive task file at `docs/dev/tasks/{feature-name}.md` containing:

- **Detailed Requirements**: Business value and acceptance criteria
- **Architecture Analysis**: Impact on clean architecture layers
- **TDD Task Breakdown**: Red-Green-Refactor phases with specific tasks
- **Time Estimates**: Realistic hour estimates for each task
- **Definition of Done**: Clear completion criteria
- **Risk Assessment**: Potential issues and mitigation strategies

#### TDD Integration

For code features, the command automatically structures tasks in three phases:

1. **Phase 1 (Red)**: Write failing tests first
   - Unit tests for business logic
   - Integration tests for API endpoints
   - E2E tests for user workflows

2. **Phase 2 (Green)**: Implement minimal code to pass tests
   - Domain layer implementation
   - Application layer services
   - Infrastructure layer repositories
   - API layer endpoints

3. **Phase 3 (Refactor)**: Improve code quality and performance
   - Code structure improvements
   - Error handling enhancement
   - Documentation updates

#### Clean Architecture Mapping

Tasks are organized by clean architecture layers:

- **Domain Layer**: Core business logic and entities
- **Application Layer**: Use cases and service orchestration
- **Infrastructure Layer**: External integrations and repositories
- **API Layer**: HTTP endpoints and request/response handling

## Task Management

Task files are stored in `docs/dev/tasks/` and can be used for:

- Sprint planning and estimation
- Progress tracking during development
- Code review checklists
- Documentation of implementation decisions

## Integration

The `/plan` command integrates with other Claude Code capabilities:

- Uses codebase analysis for architecture understanding
- Connects to implementation commands for task execution
- Works with testing commands for validation strategy
- Integrates with documentation commands for updates

## Best Practices

1. **Be Specific**: Provide clear feature descriptions
2. **Use Interactive Mode**: For complex features requiring clarification
3. **Consider Scope**: Choose appropriate scope (small/medium/large)
4. **Review Generated Tasks**: Validate estimates and requirements
5. **Update as Needed**: Modify task files based on implementation learnings