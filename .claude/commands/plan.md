# /plan - Feature Planning and Task Breakdown

## Purpose
Intelligent feature planning that breaks down complex features into actionable tasks with TDD methodology. Creates detailed task files for tracking and implementation.

## Usage
```
/plan [feature-name] [--type feature|refactor|bug|infrastructure] [--scope small|medium|large] [--interactive]
```

## Arguments
- `feature-name` - Name or description of the feature to plan (required)
- `--type` - Type of work: feature, refactor, bug, infrastructure (default: feature)
- `--scope` - Estimated scope: small (1-3 days), medium (4-7 days), large (1-3 weeks)
- `--interactive` - Interactive mode with detailed questioning
- `--dry-run` - Show planning output without creating files

## Planning Process

### 1. Requirements Gathering
If feature requirements are unclear, ask clarifying questions:

**For Features:**
- What is the business value and user benefit?
- What are the acceptance criteria?
- What are the input/output requirements?
- Are there any dependencies or constraints?
- What is the expected user workflow?
- Are there performance requirements?
- What error cases need handling?

**For Refactoring:**
- What code needs refactoring and why?
- What are the quality goals (performance, maintainability, readability)?
- What are the risks and mitigation strategies?
- Are there breaking changes expected?
- What metrics will measure success?

**For Infrastructure:**
- What infrastructure components are needed?
- What are the scalability requirements?
- What are the security considerations?
- What monitoring and alerting is needed?
- What is the deployment strategy?

### 2. Architecture Analysis
- Review existing codebase structure
- Identify affected components and layers
- Determine integration points
- Assess impact on clean architecture principles
- Plan database schema changes if needed

### 3. Task Breakdown Strategy

#### For Code Features (TDD Approach):
```
Phase 1: Test-Driven Design
- [ ] Write failing unit tests for core functionality
- [ ] Write failing integration tests for API endpoints
- [ ] Write failing E2E tests for user workflows

Phase 2: Implementation
- [ ] Implement domain entities and business logic
- [ ] Implement application services and use cases
- [ ] Implement infrastructure components (repositories, clients)
- [ ] Implement API endpoints and request/response models

Phase 3: Refactoring and Polish
- [ ] Refactor for code quality and performance
- [ ] Add error handling and validation
- [ ] Update documentation and examples
- [ ] Add monitoring and logging
```

#### For Non-Code Features:
```
Phase 1: Planning and Design
- [ ] Define requirements and acceptance criteria
- [ ] Create design mockups/wireframes
- [ ] Plan data models and schemas
- [ ] Define API contracts

Phase 2: Implementation
- [ ] Implement core functionality
- [ ] Add validation and error handling
- [ ] Implement user interface components
- [ ] Add integration points

Phase 3: Testing and Documentation
- [ ] Write comprehensive tests
- [ ] Update documentation
- [ ] Perform user acceptance testing
- [ ] Deploy to staging/production
```

### 4. Task File Generation
Creates `docs/dev/tasks/{feature-name}.md` with:
- Feature overview and requirements
- Detailed task breakdown with TDD phases
- Acceptance criteria and definition of done
- Dependencies and blockers
- Time estimates and priority levels
- Testing strategy and validation steps

## Task File Template

```markdown
# {Feature Name} - Implementation Tasks

## Overview
**Type**: {feature|refactor|bug|infrastructure}
**Scope**: {small|medium|large}
**Estimated Time**: {X days/weeks}
**Priority**: {high|medium|low}

## Requirements
{Detailed requirements and acceptance criteria}

## Architecture Impact
{Description of affected components and clean architecture layers}

## Task Breakdown

### Phase 1: Test-Driven Design (TDD Red Phase)
- [ ] **Unit Tests**: {Specific test descriptions}
  - Expected to fail initially
  - Test core business logic and entities
  - Estimated time: {X hours}
  
- [ ] **Integration Tests**: {Specific test descriptions}
  - Test API endpoints and service integration
  - Test database operations and external APIs
  - Estimated time: {X hours}
  
- [ ] **E2E Tests**: {Specific test descriptions}
  - Test complete user workflows
  - Test UI interactions and data flow
  - Estimated time: {X hours}

### Phase 2: Implementation (TDD Green Phase)
- [ ] **Domain Layer**: {Specific implementation tasks}
  - Implement core entities and business rules
  - Ensure tests pass for business logic
  - Estimated time: {X hours}
  
- [ ] **Application Layer**: {Specific implementation tasks}
  - Implement use cases and service orchestration
  - Add input validation and error handling
  - Estimated time: {X hours}
  
- [ ] **Infrastructure Layer**: {Specific implementation tasks}
  - Implement repositories and external integrations
  - Add database migrations if needed
  - Estimated time: {X hours}
  
- [ ] **API Layer**: {Specific implementation tasks}
  - Implement HTTP endpoints and request/response models
  - Add OpenAPI documentation
  - Estimated time: {X hours}

### Phase 3: Refactoring and Polish (TDD Refactor Phase)
- [ ] **Code Quality**: {Specific refactoring tasks}
  - Improve code structure and readability
  - Optimize performance if needed
  - Estimated time: {X hours}
  
- [ ] **Error Handling**: {Specific error handling tasks}
  - Add comprehensive error handling
  - Implement proper HTTP status codes
  - Estimated time: {X hours}
  
- [ ] **Documentation**: {Specific documentation tasks}
  - Update API documentation
  - Add code comments and examples
  - Estimated time: {X hours}

## Definition of Done
- [ ] All tests pass (unit, integration, E2E)
- [ ] Code coverage >90%
- [ ] No linting violations
- [ ] API documentation updated
- [ ] Feature deployed and verified
- [ ] Performance requirements met
- [ ] Security review completed (if applicable)

## Dependencies
{List any dependencies or blockers}

## Risks and Mitigations
{Identify potential risks and mitigation strategies}

## Testing Strategy
{Detailed testing approach and validation steps}

## Deployment Considerations
{Any special deployment or configuration requirements}
```

## Execution Steps

1. **Analyze Request**: Parse feature description and arguments
2. **Gather Requirements**: Ask clarifying questions if needed
3. **Review Codebase**: Understand current architecture and patterns
4. **Create Task Breakdown**: Generate detailed, actionable tasks
5. **Apply TDD Methodology**: Structure tasks in Red-Green-Refactor phases
6. **Generate Task File**: Create comprehensive task file in docs/dev/tasks/
7. **Provide Summary**: Show overview of created plan and next steps

## Integration with Other Commands
- Uses `/analyze` for codebase understanding
- Integrates with `/implement` for task execution
- Works with `/test` for validation strategy
- Connects to `/document` for documentation updates

## Examples

### Planning a New Feature
```
/plan user-authentication --type feature --scope medium
```

### Planning a Refactoring Task
```
/plan optimize-database-queries --type refactor --scope small --interactive
```

### Planning Infrastructure Work
```
/plan redis-caching --type infrastructure --scope large
```

## Quality Standards
- Tasks are specific and actionable
- TDD methodology is properly applied
- Clean architecture principles are respected
- Time estimates are realistic
- Dependencies are clearly identified
- Testing strategy is comprehensive

## Notes
- Always prioritize TDD for code-related features
- Consider clean architecture impact in task breakdown
- Include both happy path and error case testing
- Provide realistic time estimates based on complexity
- Consider integration points and dependencies
- Include security and performance considerations