# Ph Stats FastAPI Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME="Ph Stats FastAPI"
APP_VERSION="0.1.0"
DEBUG=true
SECRET_KEY="your-super-secret-key-change-in-production"

# Supabase Development Configuration
SUPABASE_URL="https://your-dev-project-id.supabase.co"
SUPABASE_ANON_KEY="your-development-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-development-service-role-key"

# Optional: Direct Database Connection (for Alembic migrations)
DATABASE_URL="postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
DATABASE_ECHO=false

# Authentication Settings (managed by Supabase)
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Optional Redis Configuration (for caching)
REDIS_URL="redis://localhost:6379/0"
# Production: REDIS_URL="rediss://..." # Render.com Redis addon

# Email Configuration (optional - Supabase handles auth emails)
# MAIL_USERNAME="<EMAIL>"
# MAIL_PASSWORD="your-app-password"
# MAIL_FROM="<EMAIL>"

# Testing Configuration (use separate test project)
# Uncomment these for testing with a separate Supabase test project
# TEST_SUPABASE_URL="https://your-test-project-id.supabase.co"
# TEST_SUPABASE_ANON_KEY="your-test-anon-key"
# TEST_SUPABASE_SERVICE_ROLE_KEY="your-test-service-role-key"
# TEST_DATABASE_URL="postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# Feature Flags
ENABLE_EMAIL_VERIFICATION=true
ENABLE_WEBSOCKETS=true
ENABLE_ANALYTICS_EXPORT=true

# Development Settings
ENVIRONMENT="development"