[project]
name = "ph-stats-fastapi"
version = "0.1.0"
description = "Powerhouse Brokers daily numbers dashboard"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # Core Framework
    "fastapi[all]>=0.100.0",
    "uvicorn[standard]>=0.24.0",
    # Database & ORM
    "sqlmodel>=0.0.18",
    "asyncpg>=0.30.0",
    "alembic>=1.13.0",
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    # Templates & Static Files
    "jinja2>=3.1.0",
    "jinjax>=0.46",
    "aiofiles>=23.2.0",
    # Configuration & Settings
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    # HTTP Client & Utilities
    "httpx>=0.25.0",
    "rich>=13.7.0",
    # Email Support
    "fastapi-mail>=1.4.0",
    # Caching & Performance
    "redis>=5.0.0",
    # Development
    "superclaude>=*******",
]

[tool.uv]
dev-dependencies = [
    # Testing Framework
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",
    "pytest-html>=4.1.0",
    "pytest-mock>=3.12.0",
    # E2E Testing
    "playwright>=1.40.0",
    "factory-boy>=3.3.0",
    "freezegun>=1.2.0",
    # Code Quality
    "ruff>=0.12.8",
    "mypy>=1.17.1",
    "pre-commit>=3.6.0",
    # Security & Performance Testing
    "bandit>=1.7.0",
    "locust>=2.17.0",
    # Development Tools
    "watchfiles>=0.21.0",
    "ipython>=8.18.0",
    "assertpy>=1.1",
    "safety>=3.2.9",
]


[tool.ruff]
line-length = 120
target-version="py312"

include = ["src/**.py", "tests/**.py"]
exclude= ["src/before.py"]

lint.select = [
  "B",    # bugbear:    potential bugs
  "DTZ",  # datetimez:  warn about missing timezones
  "F",    # pyflakes:   potential errors
  "FURB", # refurb:     refurbishing and modernizing Python codebases
  "I",    # isort:      import sorting
  "PERF", # perflint:   avoid performance anti-patterns
  "PL",   # pylint:     the oldest static code analyser ( sometimes a bit pedantic)
  "RUF",  # ruff:       rules introduced by ruff
  "S",    # bandit:     security issues
  "SIM",  # simplify:   helps you simplify your code
  "UP",   # pyupgrade:  upgrade syntax or newer versions of the language
]
lint.ignore = []

[tool.ruff.lint.per-file-ignores]
# Some checks don't make sense in unit tests
"tests/*.py" = [
    "S101",    # Use of `assert` detected
    "PLR0133", # Two constants compared in a comparison, consider replacing `1 == 1"
]

[tool.pytest.ini_options]
pythonpath = [".", "src"]
testpaths = ["./tests"]
asyncio_mode = "auto"
markers = [
    "unit: Unit tests for individual components",
    "integration: Integration tests for component interactions", 
    "e2e: End-to-end tests that require a running application",
    "browser: Tests that require browser automation",
    "api: Tests that make HTTP API calls",
    "slow: Tests that may take longer to execute",
]
addopts = [
    "-v",
    "--tb=short", 
    "--strict-markers",
    "--strict-config",
]
