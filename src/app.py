"""Main FastAPI application."""

from fastapi import FastAPI

app = FastAPI(
    title="Powerhouse Stats FastAPI",
    description="Powerhouse Brokers daily numbers dashboard",
    version="0.1.0",
)


@app.get("/")
async def root() -> dict[str, str]:
    """Root endpoint returning greeting and name."""
    return {"greeting": "Hello", "name": "World"}


@app.get("/health")
async def health() -> dict[str, str]:
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "ph-stats-fastapi"}
