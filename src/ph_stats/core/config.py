"""Core configuration system using Pydantic Settings.

This module provides a comprehensive configuration system for the FastAPI application
using Pydantic Settings for environment variable validation and type safety.

Features:
- Environment variable loading with validation
- Database URL parsing and validation
- Security settings with strength validation
- External service configuration
- Logging configuration
- Environment-specific overrides
"""

import logging
import sys
from functools import lru_cache
from typing import Any
from urllib.parse import urlparse

# Security constants
MIN_SECRET_KEY_LENGTH = 32

from pydantic import Field, computed_field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support and validation.

    This class provides comprehensive configuration management with:
    - Automatic environment variable loading
    - Type validation and conversion
    - Security-focused validation rules
    - Environment-specific defaults
    - External service configuration validation

    All configuration options can be set via environment variables or .env file.
    Environment variables take precedence over .env file values.
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        validate_default=True,
    )

    # Core Application Settings
    environment: str = Field(default="development", description="Application environment")
    debug: bool = Field(default=True, description="Enable debug mode")
    secret_key: str = Field(..., description="Secret key for JWT and sessions")

    # Database Configuration
    database_url: str = Field(..., description="PostgreSQL database connection URL")

    # JWT Authentication Settings
    jwt_algorithm: str = Field(default="HS256", description="JWT signing algorithm")
    jwt_expire_minutes: int = Field(default=30, description="JWT token expiration in minutes")
    access_token_expire_minutes: int = Field(default=15, description="Access token expiration in minutes")

    # Supabase Configuration
    supabase_url: str = Field(default="", description="Supabase project URL")
    supabase_key: str = Field(default="", description="Supabase anon key")
    supabase_service_key: str = Field(default="", description="Supabase service key")

    # External Service Configuration
    box_client_id: str = Field(default="", description="Box.com API client ID")
    box_client_secret: str = Field(default="", description="Box.com API client secret")
    civic_auth_app_id: str = Field(default="", description="Civic Auth application ID")
    civic_auth_secret: str = Field(default="", description="Civic Auth secret")
    redis_url: str = Field(default="redis://localhost:6379", description="Redis connection URL")

    # CORS Configuration
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:8000",
        description="Comma-separated list of allowed CORS origins",
    )

    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="standard", description="Logging format (standard/json)")

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v:
            raise ValueError("DATABASE_URL is required")

        try:
            parsed = urlparse(v)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid database URL format")

            if not parsed.scheme.startswith(("postgresql", "postgres")):
                raise ValueError("Database URL must use postgresql scheme")

            return v
        except Exception as e:
            raise ValueError(f"Invalid database URL format: {e}") from e

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Validate secret key strength."""
        if len(v) < MIN_SECRET_KEY_LENGTH:
            raise ValueError(f"Secret key must be at least {MIN_SECRET_KEY_LENGTH} characters long")
        return v

    @field_validator("jwt_algorithm")
    @classmethod
    def validate_jwt_algorithm(cls, v: str) -> str:
        """Validate JWT algorithm is supported."""
        allowed_algorithms = ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]
        if v not in allowed_algorithms:
            raise ValueError(f"JWT algorithm must be one of: {allowed_algorithms}")
        return v

    @model_validator(mode="after")
    def validate_environment_specific_settings(self) -> "Settings":
        """Validate environment-specific settings."""
        if self.environment == "production" and self.debug:
            # Override debug in production
            self.debug = False

        return self

    @computed_field
    def db_host(self) -> str:
        """Extract database host from URL."""
        parsed = urlparse(self.database_url)
        return parsed.hostname or "localhost"

    @computed_field
    def db_port(self) -> int:
        """Extract database port from URL."""
        parsed = urlparse(self.database_url)
        return parsed.port or 5432

    @computed_field
    def db_name(self) -> str:
        """Extract database name from URL."""
        parsed = urlparse(self.database_url)
        return parsed.path.lstrip("/") if parsed.path else "postgres"

    @computed_field
    def cors_origins_list(self) -> list[str]:
        """Parse CORS origins from string to list."""
        return [origin.strip() for origin in self.cors_origins.split(",") if origin.strip()]

    def get_database_components(self) -> dict[str, Any]:
        """Get database connection components."""
        parsed = urlparse(self.database_url)
        return {
            "host": parsed.hostname or "localhost",
            "port": parsed.port or 5432,
            "database": parsed.path.lstrip("/") if parsed.path else "postgres",
            "username": parsed.username,
            "password": parsed.password,
        }

    def validate_database_connection(self) -> bool:
        """Validate database connection configuration."""
        try:
            components = self.get_database_components()
            required_fields = ["host", "database"]

            for field in required_fields:
                if not components.get(field):
                    raise ValueError(f"Database {field} is required")

            return True
        except Exception:
            return False

    def validate_external_services(self) -> dict[str, bool]:
        """Validate external service configurations."""
        return {
            "supabase": bool(self.supabase_url and self.supabase_key),
            "box": bool(self.box_client_id and self.box_client_secret),
            "civic_auth": bool(
                self.civic_auth_app_id and self.civic_auth_secret
            ),
            "redis": bool(self.redis_url),
        }

    def validate_startup_configuration(self) -> dict[str, Any]:
        """Validate all configuration at application startup.

        Returns:
            Dict with validation results and any warnings or errors.
        """
        validation_results: dict[str, Any] = {
            "database": {"valid": False, "message": ""},
            "external_services": {},
            "security": {"valid": False, "message": ""},
            "environment": {"valid": True, "message": ""},
            "warnings": [],
        }

        # Validate database configuration
        try:
            validation_results["database"]["valid"] = self.validate_database_connection()
            validation_results["database"]["message"] = "Database configuration valid"
        except Exception as e:
            validation_results["database"]["message"] = f"Database validation failed: {e}"

        # Validate external services
        validation_results["external_services"] = self.validate_external_services()

        # Validate security settings
        try:
            if len(self.secret_key) >= MIN_SECRET_KEY_LENGTH:
                validation_results["security"]["valid"] = True
                validation_results["security"]["message"] = "Security configuration valid"
            else:
                validation_results["security"]["message"] = "Secret key too short"
        except Exception as e:
            validation_results["security"]["message"] = f"Security validation failed: {e}"

        # Environment-specific warnings
        if self.environment == "production" and self.debug:
            validation_results["warnings"].append("Debug mode enabled in production")
        if self.environment == "production" and "localhost" in self.cors_origins:
            validation_results["warnings"].append("Localhost in CORS origins for production")

        return validation_results

    def configure_logging(self) -> None:
        """Configure application logging based on settings.

        Sets up structured logging with appropriate levels and formatting
        based on the environment and configuration.
        """
        # Validate log level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level.upper() not in valid_levels:
            print(f"Warning: Invalid log level '{self.log_level}', defaulting to INFO")
            log_level = logging.INFO
        else:
            log_level = getattr(logging, self.log_level.upper())

        # Configure format based on environment and format preference
        if self.log_format == "json":
            # In a real application, you'd use a proper JSON formatter like structlog
            format_string = "%(asctime)s | %(name)s | %(levelname)s | %(message)s | env=%(environment)s"
        else:
            format_string = "%(asctime)s | %(name)s | %(levelname)s | %(message)s"

        # Remove existing handlers to avoid duplication
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # Configure logging
        logging.basicConfig(
            level=log_level,
            format=format_string,
            datefmt="%Y-%m-%d %H:%M:%S",
            stream=sys.stdout,
            force=True,
        )

        # Set library log levels to reduce noise in production
        if self.environment == "production":
            logging.getLogger("uvicorn").setLevel(logging.WARNING)
            logging.getLogger("fastapi").setLevel(logging.WARNING)

        # Log configuration success
        logger = logging.getLogger(__name__)
        logger.info(f"Logging configured - Level: {self.log_level}, Format: {self.log_format}")

    def get_configuration_summary(self) -> dict[str, Any]:
        """Get a summary of current configuration (excluding sensitive data).

        Returns:
            Dict with non-sensitive configuration values for debugging.
        """
        return {
            "environment": self.environment,
            "debug": self.debug,
            "database_host": self.db_host,
            "database_port": self.db_port,
            "database_name": self.db_name,
            "jwt_algorithm": self.jwt_algorithm,
            "jwt_expire_minutes": self.jwt_expire_minutes,
            "access_token_expire_minutes": self.access_token_expire_minutes,
            "cors_origins_count": len(self.cors_origins_list()),
            "log_level": self.log_level,
            "log_format": self.log_format,
            "external_services": {
                "supabase_configured": bool(self.supabase_url and self.supabase_key),
                "box_configured": bool(self.box_client_id and self.box_client_secret),
                "civic_auth_configured": bool(self.civic_auth_app_id and self.civic_auth_secret),
                "redis_configured": bool(self.redis_url),
            },
        }


@lru_cache
def get_settings() -> Settings:
    """Get cached application settings instance.

    This function returns a cached instance of the Settings class to ensure
    configuration is loaded once and reused throughout the application lifecycle.

    Returns:
        Settings: Validated and configured settings instance.
    """
    return Settings()


def get_configuration_docs() -> dict[str, str]:
    """Get documentation for all configuration options.

    Returns:
        Dict mapping environment variable names to their descriptions.
    """
    return {
        # Core Application Settings
        "ENVIRONMENT": "Application environment (development, staging, production)",
        "DEBUG": "Enable debug mode (true/false)",
        "SECRET_KEY": "Secret key for JWT tokens and sessions (minimum 32 characters)",
        # Database Configuration
        "DATABASE_URL": "PostgreSQL database connection URL (required)",
        # JWT Authentication
        "JWT_ALGORITHM": "JWT signing algorithm (HS256, HS384, HS512, RS256, etc.)",
        "JWT_EXPIRE_MINUTES": "JWT token expiration time in minutes",
        "ACCESS_TOKEN_EXPIRE_MINUTES": "Access token expiration time in minutes",
        # Supabase Configuration
        "SUPABASE_URL": "Supabase project URL",
        "SUPABASE_KEY": "Supabase anonymous key",
        "SUPABASE_SERVICE_KEY": "Supabase service role key",
        # External Services
        "BOX_CLIENT_ID": "Box.com API client ID for file operations",
        "BOX_CLIENT_SECRET": "Box.com API client secret",
        "CIVIC_AUTH_APP_ID": "Civic Auth application ID",
        "CIVIC_AUTH_SECRET": "Civic Auth secret key",
        "REDIS_URL": "Redis connection URL for caching and background jobs",
        # Security & CORS
        "CORS_ORIGINS": "Comma-separated list of allowed CORS origins",
        # Logging
        "LOG_LEVEL": "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
        "LOG_FORMAT": "Logging format (standard or json)",
    }
