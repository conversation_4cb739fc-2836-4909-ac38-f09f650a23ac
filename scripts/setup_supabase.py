#!/usr/bin/env python3
"""
Supabase Setup and Validation Script
This script helps validate your Supabase configuration and test connections.
"""

import os
import sys
from pathlib import Path
import httpx
import asyncio
from dotenv import load_dotenv

def load_environment():
    """Load environment variables from .env file."""
    env_path = Path(__file__).parent.parent / ".env"
    if not env_path.exists():
        print("❌ .env file not found. Please copy .env.example to .env and configure it.")
        return False
    
    load_dotenv(env_path)
    return True

def check_required_vars():
    """Check if all required Supabase variables are set."""
    required_vars = [
        "SUPABASE_URL",
        "SUPABASE_ANON_KEY", 
        "SUPABASE_SERVICE_ROLE_KEY"
    ]
    
    missing_vars = []
    placeholder_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        elif "REPLACE-WITH" in value:
            placeholder_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    if placeholder_vars:
        print(f"❌ Please replace placeholder values for: {', '.join(placeholder_vars)}")
        print("   Get your actual values from: https://supabase.com/dashboard")
        return False
    
    print("✅ All required environment variables are set")
    return True

async def test_supabase_connection():
    """Test connection to Supabase."""
    supabase_url = os.getenv("SUPABASE_URL")
    anon_key = os.getenv("SUPABASE_ANON_KEY")
    
    headers = {
        "apikey": anon_key,
        "Authorization": f"Bearer {anon_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # Test REST API connection
            response = await client.get(
                f"{supabase_url}/rest/v1/",
                headers=headers,
                timeout=10.0
            )
            
            if response.status_code == 200:
                print("✅ Supabase REST API connection successful")
            else:
                print(f"❌ Supabase REST API connection failed: {response.status_code}")
                return False
            
            # Test Auth API
            auth_response = await client.get(
                f"{supabase_url}/auth/v1/settings",
                headers=headers,
                timeout=10.0
            )
            
            if auth_response.status_code == 200:
                print("✅ Supabase Auth API connection successful")
                settings = auth_response.json()
                print(f"   Site URL: {settings.get('external', {}).get('site_url', 'Not set')}")
            else:
                print(f"❌ Supabase Auth API connection failed: {auth_response.status_code}")
                return False
                
    except httpx.RequestError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def print_setup_instructions():
    """Print detailed setup instructions."""
    print("\n🚀 Supabase Setup Instructions")
    print("=" * 50)
    print("\n1. Development Project Setup:")
    print("   • Go to https://supabase.com/dashboard")
    print("   • Click 'New project'")
    print("   • Name: ph-stats-dev")
    print("   • Wait 2-3 minutes for creation")
    print("\n2. Get API Keys:")
    print("   • Go to Settings → API")
    print("   • Copy Project URL, Anon key, and Service role key")
    print("\n3. Update .env file:")
    print("   • Replace SUPABASE_URL with your Project URL")
    print("   • Replace SUPABASE_ANON_KEY with your Anon key")
    print("   • Replace SUPABASE_SERVICE_ROLE_KEY with your Service role key")
    print("\n4. Test Project Setup:")
    print("   • Repeat steps 1-3 with name: ph-stats-test")
    print("   • Update TEST_SUPABASE_* variables in .env")
    print("\n5. Run this script again to validate:")
    print("   uv run python scripts/setup_supabase.py")

async def main():
    """Main setup and validation function."""
    print("🔧 Ph Stats FastAPI - Supabase Configuration Validator")
    print("=" * 55)
    
    # Load environment
    if not load_environment():
        print_setup_instructions()
        sys.exit(1)
    
    # Check required variables
    if not check_required_vars():
        print_setup_instructions()
        sys.exit(1)
    
    # Test connection
    print("\n🔍 Testing Supabase connections...")
    if await test_supabase_connection():
        print("\n✅ All tests passed! Your Supabase configuration is working correctly.")
        print("\n🎯 Next steps:")
        print("   1. Start your development server: uv run uvicorn src.ph_stats.main:app --reload")
        print("   2. Visit http://localhost:8000")
        print("   3. Check the API docs at http://localhost:8000/docs")
    else:
        print("\n❌ Connection tests failed. Please check your configuration.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())